<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="com.obiscr.chatgpt.settings.EasyCodeState">
    <option name="projectFiles" value="$PROJECT_DIR$/2025/123.py;E:/实验代码备份/project 1 trajectory cluster/2025/AgglomerativeClustering.py;E:/实验代码备份/project 1 trajectory cluster/2025/aisdata_process.py;E:/实验代码备份/project 1 trajectory cluster/2025/dbscan.py;E:/实验代码备份/project 1 trajectory cluster/2025/dbscan1.py;E:/实验代码备份/project 1 trajectory cluster/2025/kmeans.py;E:/实验代码备份/project 1 trajectory cluster/2025/main修改.py;E:/实验代码备份/project 1 trajectory cluster/2025/Multi-clustering2.py;E:/实验代码备份/project 1 trajectory cluster/2025/Multi-clustering3.py;E:/实验代码备份/project 1 trajectory cluster/2025/SpectralClustering.py;E:/实验代码备份/project 1 trajectory cluster/2025/原始数据展示.py;E:/实验代码备份/project 1 trajectory cluster/2025/数据提取.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/autocompletion.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/base_command.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/cmdoptions.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/command_context.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/main.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/main_parser.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/parser.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/progress_bars.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/req_command.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/spinners.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cli/status_codes.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/cache.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/check.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/completion.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/configuration.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/debug.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/download.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/freeze.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/hash.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/help.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/index.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/inspect.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/install.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/list.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/search.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/show.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/uninstall.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/commands/wheel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/distributions/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/distributions/base.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/distributions/installed.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/distributions/sdist.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/distributions/wheel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/index/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/index/collector.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/index/package_finder.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/index/sources.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/locations/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/locations/_distutils.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/locations/_sysconfig.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/locations/base.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/metadata/importlib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/metadata/importlib/_compat.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/metadata/importlib/_dists.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/metadata/importlib/_envs.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/metadata/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/metadata/_json.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/metadata/base.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/metadata/pkg_resources.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/candidate.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/direct_url.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/format_control.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/index.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/installation_report.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/link.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/scheme.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/search_scope.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/selection_prefs.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/target_python.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/models/wheel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/network/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/network/auth.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/network/cache.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/network/download.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/network/lazy_wheel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/network/session.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/network/utils.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/network/xmlrpc.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/build/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/build/build_tracker.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/build/metadata.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/build/metadata_editable.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/build/metadata_legacy.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/build/wheel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/build/wheel_editable.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/build/wheel_legacy.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/install/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/install/editable_legacy.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/install/wheel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/check.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/freeze.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/operations/prepare.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/req/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/req/constructors.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/req/req_file.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/req/req_install.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/req/req_set.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/req/req_uninstall.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/legacy/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/legacy/resolver.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/resolvelib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/resolvelib/base.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/resolvelib/candidates.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/resolvelib/factory.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/resolvelib/found_candidates.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/resolvelib/provider.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/resolvelib/reporter.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/resolvelib/requirements.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/resolvelib/resolver.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/resolution/base.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/_jaraco_text.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/_log.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/appdirs.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/compat.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/compatibility_tags.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/datetime.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/deprecation.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/direct_url_helpers.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/egg_link.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/encoding.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/entrypoints.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/filesystem.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/filetypes.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/glibc.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/hashes.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/inject_securetransport.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/logging.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/misc.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/models.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/packaging.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/setuptools_build.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/subprocess.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/temp_dir.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/unpacking.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/urls.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/virtualenv.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/utils/wheel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/vcs/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/vcs/bazaar.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/vcs/git.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/vcs/mercurial.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/vcs/subversion.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/vcs/versioncontrol.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/build_env.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/cache.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/configuration.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/main.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/pyproject.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/self_outdated_check.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_internal/wheel_builder.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/caches/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/caches/file_cache.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/caches/redis_cache.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/_cmd.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/adapter.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/cache.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/compat.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/controller.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/filewrapper.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/heuristics.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/serialize.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/cachecontrol/wrapper.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/certifi/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/certifi/__main__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/certifi/core.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/cli/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/cli/chardetect.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/metadata/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/metadata/languages.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/big5freq.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/big5prober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/chardistribution.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/charsetgroupprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/charsetprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/codingstatemachine.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/codingstatemachinedict.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/cp949prober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/enums.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/escprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/escsm.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/eucjpprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/euckrfreq.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/euckrprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/euctwfreq.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/euctwprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/gb2312freq.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/gb2312prober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/hebrewprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/jisfreq.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/johabfreq.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/johabprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/jpcntx.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/langbulgarianmodel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/langgreekmodel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/langhebrewmodel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/langhungarianmodel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/langrussianmodel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/langthaimodel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/langturkishmodel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/latin1prober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/macromanprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/mbcharsetprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/mbcsgroupprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/mbcssm.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/resultdict.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/sbcharsetprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/sbcsgroupprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/sjisprober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/universaldetector.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/utf1632prober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/utf8prober.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/chardet/version.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/tests/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/tests/ansi_test.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/tests/ansitowin32_test.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/tests/initialise_test.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/tests/isatty_test.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/tests/utils.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/tests/winterm_test.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/ansi.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/ansitowin32.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/initialise.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/win32.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/colorama/winterm.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/compat.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/database.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/index.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/locators.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/manifest.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/markers.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/metadata.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/resources.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/scripts.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/util.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/version.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distlib/wheel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distro/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distro/__main__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/distro/distro.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/idna/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/idna/codec.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/idna/compat.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/idna/core.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/idna/idnadata.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/idna/intranges.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/idna/package_data.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/idna/uts46data.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/msgpack/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/msgpack/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/msgpack/ext.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/msgpack/fallback.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/__about__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/_manylinux.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/_musllinux.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/_structures.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/markers.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/requirements.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/specifiers.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/tags.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/utils.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/packaging/version.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pkg_resources/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/platformdirs/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/platformdirs/__main__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/platformdirs/android.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/platformdirs/api.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/platformdirs/macos.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/platformdirs/unix.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/platformdirs/version.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/platformdirs/windows.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/filters/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/_mapping.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/bbcode.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/groff.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/html.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/img.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/irc.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/latex.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/other.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/pangomarkup.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/rtf.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/svg.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/terminal.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatters/terminal256.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/lexers/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/lexers/_mapping.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/lexers/python.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/styles/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/__main__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/cmdline.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/console.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/filter.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/formatter.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/lexer.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/modeline.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/plugin.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/regexopt.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/scanner.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/sphinxext.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/style.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/token.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/unistring.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pygments/util.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/diagram/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/actions.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/common.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/core.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/helpers.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/results.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/testing.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/unicode.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyparsing/util.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyproject_hooks/_in_process/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyproject_hooks/_in_process/_in_process.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyproject_hooks/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyproject_hooks/_compat.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/pyproject_hooks/_impl.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/__version__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/_internal_utils.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/adapters.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/api.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/auth.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/certs.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/compat.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/cookies.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/help.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/hooks.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/models.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/packages.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/sessions.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/status_codes.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/structures.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/requests/utils.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/resolvelib/compat/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/resolvelib/compat/collections_abc.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/resolvelib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/resolvelib/providers.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/resolvelib/reporters.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/resolvelib/resolvers.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/resolvelib/structs.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/__main__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_cell_widths.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_emoji_codes.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_emoji_replace.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_export_format.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_extension.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_fileno.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_inspect.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_log_render.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_loop.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_null_file.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_palettes.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_pick.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_ratio.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_spinners.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_stack.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_timer.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_win32_console.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_windows.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_windows_renderer.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/_wrap.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/abc.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/align.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/ansi.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/bar.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/box.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/cells.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/color.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/color_triplet.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/columns.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/console.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/constrain.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/containers.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/control.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/default_styles.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/diagnose.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/emoji.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/errors.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/file_proxy.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/filesize.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/highlighter.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/json.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/jupyter.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/layout.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/live.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/live_render.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/logging.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/markup.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/measure.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/padding.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/pager.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/palette.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/panel.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/pretty.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/progress.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/progress_bar.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/prompt.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/protocol.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/region.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/repr.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/rule.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/scope.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/screen.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/segment.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/spinner.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/status.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/style.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/styled.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/syntax.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/table.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/terminal_theme.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/text.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/theme.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/themes.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/traceback.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/rich/tree.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/_asyncio.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/_utils.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/after.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/before.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/before_sleep.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/nap.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/retry.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/stop.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/tornadoweb.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tenacity/wait.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tomli/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tomli/_parser.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tomli/_re.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/tomli/_types.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/bindings.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/low_level.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/contrib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_appengine_environ.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/contrib/appengine.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/contrib/ntlmpool.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/contrib/pyopenssl.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/contrib/securetransport.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/contrib/socks.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/packages/backports/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/packages/backports/makefile.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/packages/backports/weakref_finalize.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/packages/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/packages/six.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/connection.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/proxy.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/queue.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/request.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/response.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/retry.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/ssl_.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/ssl_match_hostname.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/ssltransport.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/timeout.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/url.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/util/wait.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/_collections.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/_version.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/connection.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/connectionpool.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/fields.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/filepost.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/poolmanager.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/request.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/urllib3/response.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/webencodings/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/webencodings/labels.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/webencodings/mklabels.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/webencodings/tests.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/webencodings/x_user_defined.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/six.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/_vendor/typing_extensions.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/__init__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/__main__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/pip/__pip-runner__.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Lib/site-packages/_virtualenv.py;E:/实验代码备份/project 1 trajectory cluster/pythonProject/.venv/Scripts/activate_this.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/_distutils_hack/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/_distutils_hack/override.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/autocompletion.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/base_command.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/cmdoptions.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/command_context.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/main.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/main_parser.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/parser.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/progress_bars.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/req_command.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/spinners.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cli/status_codes.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/cache.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/check.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/completion.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/configuration.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/debug.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/download.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/freeze.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/hash.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/help.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/index.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/install.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/list.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/search.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/show.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/uninstall.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/commands/wheel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/distributions/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/distributions/base.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/distributions/installed.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/distributions/sdist.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/distributions/wheel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/index/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/index/collector.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/index/package_finder.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/index/sources.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/locations/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/locations/_distutils.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/locations/_sysconfig.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/locations/base.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/metadata/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/metadata/base.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/metadata/pkg_resources.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/candidate.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/direct_url.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/format_control.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/index.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/link.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/scheme.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/search_scope.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/selection_prefs.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/target_python.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/models/wheel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/network/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/network/auth.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/network/cache.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/network/download.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/network/lazy_wheel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/network/session.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/network/utils.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/network/xmlrpc.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/build/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/build/metadata.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/build/metadata_editable.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/build/metadata_legacy.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/build/wheel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/build/wheel_editable.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/build/wheel_legacy.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/install/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/install/editable_legacy.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/install/legacy.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/install/wheel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/check.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/freeze.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/operations/prepare.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/req/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/req/constructors.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/req/req_file.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/req/req_install.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/req/req_set.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/req/req_tracker.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/req/req_uninstall.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/legacy/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/legacy/resolver.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/base.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/candidates.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/factory.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/found_candidates.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/provider.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/reporter.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/requirements.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/resolver.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/resolution/base.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/_log.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/appdirs.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/compatibility_tags.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/datetime.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/deprecation.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/direct_url_helpers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/distutils_args.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/egg_link.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/encoding.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/entrypoints.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/filesystem.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/filetypes.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/glibc.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/hashes.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/inject_securetransport.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/logging.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/misc.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/models.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/packaging.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/parallel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/pkg_resources.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/setuptools_build.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/subprocess.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/temp_dir.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/unpacking.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/urls.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/virtualenv.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/utils/wheel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/vcs/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/vcs/bazaar.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/vcs/git.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/vcs/mercurial.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/vcs/subversion.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/vcs/versioncontrol.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/build_env.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/cache.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/configuration.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/main.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/pyproject.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/self_outdated_check.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_internal/wheel_builder.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/caches/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/caches/file_cache.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/caches/redis_cache.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/_cmd.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/adapter.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/cache.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/controller.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/filewrapper.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/heuristics.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/serialize.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/cachecontrol/wrapper.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/certifi/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/certifi/__main__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/certifi/core.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/cli/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/cli/chardetect.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/metadata/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/metadata/languages.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/big5freq.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/big5prober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/chardistribution.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/charsetgroupprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/charsetprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/codingstatemachine.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/cp949prober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/enums.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/escprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/escsm.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/eucjpprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/euckrfreq.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/euckrprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/euctwfreq.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/euctwprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/gb2312freq.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/gb2312prober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/hebrewprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/jisfreq.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/jpcntx.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/langbulgarianmodel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/langgreekmodel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/langhebrewmodel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/langhungarianmodel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/langrussianmodel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/langthaimodel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/langturkishmodel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/latin1prober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/mbcharsetprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/mbcsgroupprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/mbcssm.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/sbcharsetprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/sbcsgroupprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/sjisprober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/universaldetector.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/utf8prober.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/chardet/version.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/colorama/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/colorama/ansi.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/colorama/ansitowin32.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/colorama/initialise.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/colorama/win32.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/colorama/winterm.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/_backport/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/_backport/misc.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/_backport/shutil.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/_backport/sysconfig.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/_backport/tarfile.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/database.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/index.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/locators.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/manifest.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/markers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/metadata.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/resources.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/scripts.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/util.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/version.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distlib/wheel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/_trie/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/_trie/_base.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/_trie/py.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/filters/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/filters/alphabeticalattributes.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/filters/base.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/filters/inject_meta_charset.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/filters/lint.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/filters/optionaltags.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/filters/sanitizer.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/filters/whitespace.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treeadapters/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treeadapters/genshi.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treeadapters/sax.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treebuilders/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treebuilders/base.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treebuilders/dom.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treebuilders/etree.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treebuilders/etree_lxml.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/base.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/dom.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/etree.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/etree_lxml.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/genshi.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/_ihatexml.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/_inputstream.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/_tokenizer.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/_utils.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/constants.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/html5parser.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/html5lib/serializer.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/idna/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/idna/codec.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/idna/compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/idna/core.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/idna/idnadata.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/idna/intranges.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/idna/package_data.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/idna/uts46data.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/msgpack/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/msgpack/_version.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/msgpack/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/msgpack/ext.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/msgpack/fallback.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/__about__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/_manylinux.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/_musllinux.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/_structures.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/markers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/requirements.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/specifiers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/tags.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/utils.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/packaging/version.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/in_process/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/in_process/_in_process.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/build.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/check.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/colorlog.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/dirtools.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/envbuild.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/meta.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pep517/wrappers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pkg_resources/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pkg_resources/py31compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/platformdirs/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/platformdirs/__main__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/platformdirs/android.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/platformdirs/api.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/platformdirs/macos.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/platformdirs/unix.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/platformdirs/version.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/platformdirs/windows.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/progress/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/progress/bar.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/progress/colors.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/progress/counter.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/progress/spinner.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/__version__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/_internal_utils.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/adapters.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/api.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/auth.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/certs.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/cookies.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/help.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/hooks.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/models.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/packages.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/sessions.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/status_codes.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/structures.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/requests/utils.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/resolvelib/compat/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/resolvelib/compat/collections_abc.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/resolvelib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/resolvelib/providers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/resolvelib/reporters.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/resolvelib/resolvers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/resolvelib/structs.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/_asyncio.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/_utils.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/after.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/before.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/before_sleep.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/nap.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/retry.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/stop.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/tornadoweb.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tenacity/wait.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tomli/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tomli/_parser.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/tomli/_re.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/bindings.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/low_level.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_appengine_environ.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/appengine.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/ntlmpool.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/pyopenssl.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/securetransport.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/socks.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/packages/backports/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/packages/backports/makefile.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/packages/ssl_match_hostname/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/packages/ssl_match_hostname/_implementation.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/packages/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/packages/six.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/connection.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/proxy.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/queue.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/request.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/response.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/retry.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/ssl_.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/ssltransport.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/timeout.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/url.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/util/wait.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/_collections.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/_version.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/connection.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/connectionpool.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/fields.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/filepost.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/poolmanager.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/request.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/urllib3/response.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/webencodings/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/webencodings/labels.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/webencodings/mklabels.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/webencodings/tests.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/webencodings/x_user_defined.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/distro.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/pyparsing.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/_vendor/six.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pip/__main__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/__about__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/_manylinux.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/_musllinux.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/_structures.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/markers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/requirements.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/specifiers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/tags.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/utils.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/packaging/version.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/appdirs.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/_vendor/pyparsing.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/extern/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/pkg_resources/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/bdist.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/bdist_msi.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/bdist_wininst.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/build.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/build_clib.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/build_ext.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/build_py.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/build_scripts.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/check.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/clean.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/config.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/install.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/install_data.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/install_egg_info.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/install_headers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/install_lib.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/install_scripts.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/py37compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/register.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/sdist.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/command/upload.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/_msvccompiler.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/archive_util.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/bcppcompiler.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/ccompiler.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/cmd.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/config.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/core.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/cygwinccompiler.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/debug.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/dep_util.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/dir_util.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/dist.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/errors.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/extension.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/fancy_getopt.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/file_util.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/filelist.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/log.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/msvc9compiler.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/msvccompiler.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/py35compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/py38compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/spawn.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/sysconfig.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/text_file.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/unixccompiler.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/util.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/version.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_distutils/versionpredicate.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/more_itertools/more.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/__about__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/_structures.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/markers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/requirements.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/specifiers.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/tags.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/utils.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/packaging/version.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/ordered_set.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_vendor/pyparsing.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/alias.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/bdist_egg.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/bdist_rpm.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/build_clib.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/build_ext.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/build_py.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/develop.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/dist_info.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/easy_install.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/egg_info.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/install.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/install_egg_info.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/install_lib.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/install_scripts.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/py36compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/register.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/rotate.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/saveopts.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/sdist.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/setopt.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/test.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/upload.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/command/upload_docs.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/extern/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/__init__.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_deprecation_warning.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/_imp.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/archive_util.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/build_meta.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/config.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/dep_util.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/depends.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/dist.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/errors.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/extension.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/glob.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/installer.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/launch.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/monkey.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/msvc.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/namespaces.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/package_index.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/py34compat.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/sandbox.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/unicode_utils.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/version.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/wheel.py;E:/实验代码备份/project 1 trajectory cluster/venv/Lib/site-packages/setuptools/windows_support.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/_distutils_hack/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/_distutils_hack/override.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/autocompletion.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/base_command.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/cmdoptions.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/command_context.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/main.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/main_parser.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/parser.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/progress_bars.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/req_command.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/spinners.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cli/status_codes.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/cache.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/check.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/completion.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/configuration.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/debug.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/download.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/freeze.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/hash.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/help.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/index.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/install.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/list.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/search.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/show.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/uninstall.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/commands/wheel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/distributions/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/distributions/base.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/distributions/installed.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/distributions/sdist.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/distributions/wheel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/index/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/index/collector.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/index/package_finder.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/index/sources.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/locations/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/locations/_distutils.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/locations/_sysconfig.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/locations/base.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/metadata/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/metadata/base.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/metadata/pkg_resources.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/candidate.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/direct_url.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/format_control.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/index.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/link.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/scheme.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/search_scope.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/selection_prefs.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/target_python.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/models/wheel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/network/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/network/auth.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/network/cache.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/network/download.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/network/lazy_wheel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/network/session.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/network/utils.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/network/xmlrpc.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/build/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/build/metadata.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/build/metadata_editable.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/build/metadata_legacy.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/build/wheel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/build/wheel_editable.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/build/wheel_legacy.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/install/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/install/editable_legacy.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/install/legacy.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/install/wheel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/check.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/freeze.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/operations/prepare.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/req/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/req/constructors.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/req/req_file.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/req/req_install.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/req/req_set.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/req/req_tracker.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/req/req_uninstall.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/legacy/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/legacy/resolver.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/base.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/candidates.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/factory.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/found_candidates.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/provider.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/reporter.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/requirements.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/resolvelib/resolver.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/resolution/base.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/_log.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/appdirs.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/compatibility_tags.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/datetime.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/deprecation.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/direct_url_helpers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/distutils_args.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/egg_link.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/encoding.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/entrypoints.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/filesystem.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/filetypes.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/glibc.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/hashes.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/inject_securetransport.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/logging.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/misc.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/models.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/packaging.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/parallel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/pkg_resources.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/setuptools_build.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/subprocess.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/temp_dir.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/unpacking.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/urls.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/virtualenv.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/utils/wheel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/vcs/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/vcs/bazaar.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/vcs/git.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/vcs/mercurial.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/vcs/subversion.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/vcs/versioncontrol.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/build_env.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/cache.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/configuration.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/main.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/pyproject.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/self_outdated_check.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_internal/wheel_builder.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/caches/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/caches/file_cache.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/caches/redis_cache.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/_cmd.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/adapter.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/cache.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/controller.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/filewrapper.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/heuristics.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/serialize.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/cachecontrol/wrapper.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/certifi/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/certifi/__main__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/certifi/core.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/cli/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/cli/chardetect.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/metadata/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/metadata/languages.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/big5freq.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/big5prober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/chardistribution.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/charsetgroupprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/charsetprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/codingstatemachine.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/cp949prober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/enums.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/escprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/escsm.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/eucjpprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/euckrfreq.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/euckrprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/euctwfreq.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/euctwprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/gb2312freq.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/gb2312prober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/hebrewprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/jisfreq.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/jpcntx.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/langbulgarianmodel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/langgreekmodel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/langhebrewmodel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/langhungarianmodel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/langrussianmodel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/langthaimodel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/langturkishmodel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/latin1prober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/mbcharsetprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/mbcsgroupprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/mbcssm.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/sbcharsetprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/sbcsgroupprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/sjisprober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/universaldetector.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/utf8prober.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/chardet/version.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/colorama/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/colorama/ansi.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/colorama/ansitowin32.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/colorama/initialise.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/colorama/win32.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/colorama/winterm.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/_backport/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/_backport/misc.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/_backport/shutil.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/_backport/sysconfig.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/_backport/tarfile.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/database.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/index.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/locators.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/manifest.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/markers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/metadata.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/resources.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/scripts.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/util.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/version.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distlib/wheel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/_trie/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/_trie/_base.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/_trie/py.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/filters/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/filters/alphabeticalattributes.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/filters/base.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/filters/inject_meta_charset.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/filters/lint.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/filters/optionaltags.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/filters/sanitizer.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/filters/whitespace.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treeadapters/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treeadapters/genshi.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treeadapters/sax.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treebuilders/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treebuilders/base.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treebuilders/dom.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treebuilders/etree.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treebuilders/etree_lxml.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/base.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/dom.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/etree.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/etree_lxml.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/treewalkers/genshi.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/_ihatexml.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/_inputstream.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/_tokenizer.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/_utils.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/constants.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/html5parser.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/html5lib/serializer.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/idna/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/idna/codec.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/idna/compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/idna/core.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/idna/idnadata.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/idna/intranges.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/idna/package_data.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/idna/uts46data.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/msgpack/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/msgpack/_version.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/msgpack/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/msgpack/ext.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/msgpack/fallback.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/__about__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/_manylinux.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/_musllinux.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/_structures.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/markers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/requirements.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/specifiers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/tags.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/utils.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/packaging/version.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/in_process/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/in_process/_in_process.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/build.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/check.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/colorlog.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/dirtools.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/envbuild.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/meta.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pep517/wrappers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pkg_resources/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pkg_resources/py31compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/platformdirs/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/platformdirs/__main__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/platformdirs/android.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/platformdirs/api.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/platformdirs/macos.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/platformdirs/unix.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/platformdirs/version.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/platformdirs/windows.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/progress/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/progress/bar.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/progress/colors.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/progress/counter.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/progress/spinner.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/__version__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/_internal_utils.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/adapters.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/api.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/auth.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/certs.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/cookies.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/help.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/hooks.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/models.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/packages.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/sessions.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/status_codes.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/structures.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/requests/utils.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/resolvelib/compat/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/resolvelib/compat/collections_abc.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/resolvelib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/resolvelib/providers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/resolvelib/reporters.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/resolvelib/resolvers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/resolvelib/structs.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/_asyncio.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/_utils.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/after.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/before.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/before_sleep.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/nap.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/retry.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/stop.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/tornadoweb.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tenacity/wait.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tomli/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tomli/_parser.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/tomli/_re.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/bindings.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/low_level.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/_appengine_environ.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/appengine.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/ntlmpool.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/pyopenssl.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/securetransport.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/contrib/socks.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/packages/backports/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/packages/backports/makefile.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/packages/ssl_match_hostname/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/packages/ssl_match_hostname/_implementation.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/packages/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/packages/six.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/connection.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/proxy.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/queue.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/request.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/response.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/retry.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/ssl_.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/ssltransport.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/timeout.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/url.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/util/wait.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/_collections.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/_version.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/connection.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/connectionpool.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/exceptions.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/fields.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/filepost.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/poolmanager.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/request.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/urllib3/response.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/webencodings/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/webencodings/labels.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/webencodings/mklabels.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/webencodings/tests.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/webencodings/x_user_defined.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/distro.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/pyparsing.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/_vendor/six.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pip/__main__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/__about__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/_manylinux.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/_musllinux.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/_structures.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/markers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/requirements.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/specifiers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/tags.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/utils.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/packaging/version.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/appdirs.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/_vendor/pyparsing.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/extern/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/pkg_resources/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/bdist.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/bdist_msi.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/bdist_wininst.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/build.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/build_clib.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/build_ext.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/build_py.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/build_scripts.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/check.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/clean.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/config.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/install.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/install_data.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/install_egg_info.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/install_headers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/install_lib.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/install_scripts.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/py37compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/register.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/sdist.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/command/upload.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/_msvccompiler.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/archive_util.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/bcppcompiler.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/ccompiler.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/cmd.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/config.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/core.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/cygwinccompiler.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/debug.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/dep_util.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/dir_util.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/dist.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/errors.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/extension.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/fancy_getopt.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/file_util.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/filelist.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/log.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/msvc9compiler.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/msvccompiler.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/py35compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/py38compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/spawn.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/sysconfig.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/text_file.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/unixccompiler.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/util.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/version.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_distutils/versionpredicate.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/more_itertools/more.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/__about__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/_structures.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/markers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/requirements.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/specifiers.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/tags.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/utils.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/packaging/version.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/ordered_set.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_vendor/pyparsing.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/alias.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/bdist_egg.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/bdist_rpm.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/build_clib.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/build_ext.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/build_py.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/develop.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/dist_info.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/easy_install.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/egg_info.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/install.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/install_egg_info.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/install_lib.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/install_scripts.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/py36compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/register.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/rotate.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/saveopts.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/sdist.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/setopt.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/test.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/upload.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/command/upload_docs.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/extern/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/__init__.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_deprecation_warning.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/_imp.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/archive_util.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/build_meta.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/config.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/dep_util.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/depends.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/dist.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/errors.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/extension.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/glob.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/installer.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/launch.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/monkey.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/msvc.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/namespaces.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/package_index.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/py34compat.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/sandbox.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/unicode_utils.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/version.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/wheel.py;E:/实验代码备份/project 1 trajectory cluster/Version2/venv/Lib/site-packages/setuptools/windows_support.py;E:/实验代码备份/project 1 trajectory cluster/all_data_test.py;E:/实验代码备份/project 1 trajectory cluster/Comb_main.py;E:/实验代码备份/project 1 trajectory cluster/Comb_main_1.py;E:/实验代码备份/project 1 trajectory cluster/main - 副本.py;E:/实验代码备份/project 1 trajectory cluster/main.py;E:/实验代码备份/project 1 trajectory cluster/main02.py;E:/实验代码备份/project 1 trajectory cluster/main03.py;E:/实验代码备份/project 1 trajectory cluster/main04.py;E:/实验代码备份/project 1 trajectory cluster/main_01 - 副本.py;E:/实验代码备份/project 1 trajectory cluster/main_01.py;E:/实验代码备份/project 1 trajectory cluster/steps.py;E:/实验代码备份/project 1 trajectory cluster/test.py" />
  </component>
</project>