"""
AIS船舶轨迹聚类质量评估工具
功能：计算轮廓系数(<PERSON>l<PERSON><PERSON> Coefficient)和戴维森堡丁指数(Davies-Bouldin Index)
"""

import pandas as pd
import numpy as np
from sklearn.metrics import silhouette_score, davies_bouldin_score
import warnings

warnings.filterwarnings('ignore')

def load_ais_data(file_path, longitude_col='longitude', latitude_col='latitude',
                  heading_col='heading', cluster_col='cluster_labels'):
    """
    加载AIS数据
    """
    try:
        # 根据文件扩展名选择读取方式
        if file_path.endswith('.csv'):
            data = pd.read_csv(file_path, encoding='utf-8')
        elif file_path.endswith(('.xlsx', '.xls')):
            data = pd.read_excel(file_path)
        else:
            print("不支持的文件格式，请使用CSV或Excel文件")
            return None

        print(f"成功加载数据，共 {len(data)} 条记录")

        # 检查必需的列是否存在
        required_cols = [longitude_col, latitude_col, heading_col, cluster_col]
        missing_cols = [col for col in required_cols if col not in data.columns]

        if missing_cols:
            print(f"错误：缺少以下列: {missing_cols}")
            return None

        return data

    except Exception as e:
        print(f"数据加载失败: {str(e)}")
        return None


def preprocess_ais_data(data, longitude_col='longitude', latitude_col='latitude',
                       heading_col='heading', cluster_col='cluster_labels'):
    """
    数据预处理，提取特征和标签
    """
    try:
        # 提取特征列
        feature_cols = [longitude_col, latitude_col, heading_col]

        # 检查缺失值
        missing_count = data[feature_cols + [cluster_col]].isnull().sum()
        if missing_count.sum() > 0:
            print("发现缺失值，正在清理...")
            print(missing_count)

        # 删除包含缺失值的行
        clean_data = data[feature_cols + [cluster_col]].dropna()
        print(f"清理后数据量: {len(clean_data)} 条记录")

        # 提取特征和标签
        features = clean_data[feature_cols].values
        labels = clean_data[cluster_col].values

        # 检查聚类标签
        unique_labels = np.unique(labels)
        print(f"聚类标签: {unique_labels}")
        print(f"聚类数量: {len(unique_labels)}")

        if len(unique_labels) < 2:
            print("错误：聚类数量少于2，无法进行评估")
            return None, None

        return features, labels

    except Exception as e:
        print(f"数据预处理失败: {str(e)}")
        return None, None


def calculate_clustering_metrics(features, labels):
    """
    计算聚类评估指标
    返回：轮廓系数, 戴维森堡丁指数
    """
    try:
        # 计算轮廓系数
        silhouette_coef = silhouette_score(features, labels)

        # 计算戴维森堡丁指数
        davies_bouldin_idx = davies_bouldin_score(features, labels)

        return silhouette_coef, davies_bouldin_idx

    except Exception as e:
        print(f"指标计算失败: {str(e)}")
        return None, None

def print_results(silhouette_coef, davies_bouldin_idx, features, labels):
    """
    打印评估结果
    """
    print("\n=== AIS聚类质量评估结果 ===")
    print(f"数据样本数: {len(features)}")
    print(f"聚类数量: {len(np.unique(labels))}")
    print(f"\n轮廓系数 (Silhouette Coefficient): {silhouette_coef:.4f}")
    print("  - 取值范围: [-1, 1]，值越接近1表示聚类效果越好")
    print(f"\n戴维森堡丁指数 (Davies-Bouldin Index): {davies_bouldin_idx:.4f}")
    print("  - 取值范围: [0, +∞)，值越小表示聚类效果越好")

    # 聚类标签分布
    unique_labels, counts = np.unique(labels, return_counts=True)
    print(f"\n聚类分布:")
    for label, count in zip(unique_labels, counts):
        percentage = count / len(labels) * 100
        print(f"  簇 {label}: {count} 个样本 ({percentage:.1f}%)")


def evaluate_ais_clustering(file_path, longitude_col='longitude', latitude_col='latitude',
                           heading_col='heading', cluster_col='cluster_labels'):
    """
    完整的AIS聚类评估流程
    """
    print("开始AIS聚类质量评估...")

    # 1. 加载数据
    data = load_ais_data(file_path, longitude_col, latitude_col, heading_col, cluster_col)
    if data is None:
        return

    # 2. 数据预处理
    features, labels = preprocess_ais_data(data, longitude_col, latitude_col, heading_col, cluster_col)
    if features is None:
        return

    # 3. 计算评估指标
    silhouette_coef, davies_bouldin_idx = calculate_clustering_metrics(features, labels)
    if silhouette_coef is None:
        return

    # 4. 打印结果
    print_results(silhouette_coef, davies_bouldin_idx, features, labels)

    return silhouette_coef, davies_bouldin_idx


def main():
    """
    主函数 - 使用示例
    """
    # 请修改为您的实际数据文件路径
    file_path = "your_ais_data.csv"

    # 如果文件不存在，显示使用说明
    import os
    if not os.path.exists(file_path):
        print("请将您的AIS数据文件路径替换到 file_path 变量中")
        print("\n数据文件应包含以下列:")
        print("- longitude: 经度")
        print("- latitude: 纬度")
        print("- heading: 航向")
        print("- cluster_labels: 聚类标签 (0, 1, 2, ...)")
        return

    # 运行评估
    evaluate_ais_clustering(
        file_path=file_path,
        longitude_col='longitude',    # 根据实际列名调整
        latitude_col='latitude',      # 根据实际列名调整
        heading_col='heading',        # 根据实际列名调整
        cluster_col='cluster_labels'  # 根据实际列名调整
    )


if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
