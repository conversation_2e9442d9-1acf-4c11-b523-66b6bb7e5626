船舶自动识别系统（AIS）由岸上基站设施和船上设备组成。 它是一种集网络技术、现代通信技术、计算机技术和电子信息显示技术为一体的新型数字助航系统和设备。 船舶自动识别系统可以提供船舶之间以及船舶与岸上基站之间的通信和管理，因此在航海信息化的过程中，AIS系统受到越来越多的关注。 船舶自动识别系统的报文数据中包含了大量的海上船舶信息。 为了在AIS数据中发现特定区域内船舶的运动规律，提出了基于船舶轨迹段密度的改进DBSCAN算法。 采用改进的DBSCAN聚类算法对真实AIS航迹段数据进行聚类分析，而不是对整个航迹进行聚类分析。 实验结果表明，改进的DBSCAN算法能够得到区域内的共同航迹，在一定程度上提高了聚类的精度，为进一步分析船舶异常行为奠定了基础。

船舶自动识别系统（AIS）是船舶与船舶、船舶与海岸之间的海上保安和通信系统。

AIS可以自动交换船舶的位置、航速、航向、船名、呼号等重要信息，对于船舶航行安全和海上交通管理具有重要作用，此外，海量的AIS数据为海上交通效率的数据挖掘研究提供了基础（张C，2020），环境评价（科普S，2021），贸易分析（牟N，2021），船舶交通模式识别（Xiao Z，2019）等。轨迹提取在AIS数据的应用中起着至关重要的作用，因为它构成了许多研究的基础，包括轨迹分析和预测，异常检测、碰撞避免等。