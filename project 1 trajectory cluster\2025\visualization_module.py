"""
AIS数据聚类可视化模块
包含所有绘图相关的功能，用于生成各种类型的可视化图表
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
from scipy.interpolate import splev
from matplotlib.lines import Line2D

# 设置支持中文的字体
import matplotlib
matplotlib.rcParams['font.family'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False


class AISVisualization:
    """AIS数据可视化类"""
    
    def __init__(self, config):
        """
        初始化可视化器
        
        Args:
            config: 配置字典，包含保存路径等参数
        """
        self.config = config
        
    def visualize_all_results(self, orig_input, labels, up_red, down_red, splines):
        """
        为每个数据块生成四张特定图片
        
        Args:
            orig_input: 原始输入数据列表
            labels: 聚类标签列表
            up_red: 上游简化中心点
            down_red: 下游简化中心点
            splines: 样条拟合结果
        """
        if not os.path.exists(self.config['save_dir']):
            os.makedirs(self.config['save_dir'])

        if orig_input and labels:
            for i, (input_data, label) in enumerate(zip(orig_input, labels)):
                # 为每个数据块创建独立的文件夹
                block_dir = os.path.join(self.config['save_dir'], f'block_{i+1}')
                if not os.path.exists(block_dir):
                    os.makedirs(block_dir)

                # 图1: 原始数据对比图 - 灰色半透明小点
                self.plot_original_data_comparison(input_data, i, block_dir)

                # 图2: 聚类结果对比图 - 红色圆点（上游）和蓝色三角（下游）
                self.plot_clustering_results(input_data, label, i, block_dir)

                # 图3: KMeans中心点后的聚类图 - 红色大圆与蓝色大三角
                self.plot_kmeans_centroids(up_red, down_red, i, block_dir)

                # 图4: 拟合曲线图片 - 红色与蓝色曲线并叠加中心点
                self.plot_spline_fit(up_red, down_red, splines, i, block_dir)

                print(f"数据块 {i+1} 的图片已保存至: {block_dir}")
        else:
            print("无法生成图片，数据或标签为空。")

    def plot_original_data_comparison(self, input_data, index, save_dir):
        """绘制原始数据对比图（灰色半透明小点）"""
        plt.figure(figsize=(12, 8))
        plt.title(f"Block {index+1}: Original Data Points")

        # 使用灰色半透明小点显示原始数据
        plt.scatter(
            input_data[:, 0],
            input_data[:, 1],
            c='gray',
            marker='.',
            s=8,
            alpha=0.5,
            label='Original Data'
        )

        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.legend()
        plt.tight_layout()

        save_path = os.path.join(save_dir, 'figure1_original_data.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"图1已保存至: {save_path}")

    def plot_clustering_results(self, input_data, labels, index, save_dir):
        """绘制聚类结果对比图（红色下三角表示上游，蓝色上三角表示下游）"""
        plt.figure(figsize=(12, 8))
        plt.title(f"Block {index+1}: Clustering Results")

        # 绘制上游数据 - 红色下三角箭头
        plt.scatter(
            input_data[labels == 1, 0],
            input_data[labels == 1, 1],
            c='red',
            marker='v',  # 下三角箭头
            s=20,
            alpha=0.7,
            label='Upstream'
        )

        # 绘制下游数据 - 蓝色上三角箭头
        plt.scatter(
            input_data[labels == 0, 0],
            input_data[labels == 0, 1],
            c='blue',
            marker='^',  # 上三角箭头
            s=20,
            alpha=0.7,
            label='Downstream'
        )

        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.legend()
        plt.tight_layout()

        save_path = os.path.join(save_dir, 'figure2_clustering_results.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"图2已保存至: {save_path}")

    def plot_kmeans_centroids(self, up_red, down_red, index, save_dir):
        """绘制KMeans中心点后的聚类图（红色大下三角与蓝色大上三角）"""
        plt.figure(figsize=(12, 8))
        plt.title(f"Block {index+1}: KMeans Centroids")

        # 绘制上游KMeans中心点 - 红色大下三角
        if up_red is not None and up_red.size and up_red.ndim == 2 and up_red.shape[0] > 0:
            plt.scatter(
                up_red[:, 0],
                up_red[:, 1],
                c='red',
                marker='v',  # 下三角箭头
                s=100,
                alpha=0.8,
                label='Upstream KMeans Centers',
                edgecolors='darkred',
                linewidth=1
            )

        # 绘制下游KMeans中心点 - 蓝色大上三角
        if down_red is not None and down_red.size and down_red.ndim == 2 and down_red.shape[0] > 0:
            plt.scatter(
                down_red[:, 0],
                down_red[:, 1],
                c='blue',
                marker='^',  # 上三角箭头
                s=100,
                alpha=0.8,
                label='Downstream KMeans Centers',
                edgecolors='darkblue',
                linewidth=1
            )

        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.legend()
        plt.tight_layout()

        save_path = os.path.join(save_dir, 'figure3_kmeans_centroids.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"图3已保存至: {save_path}")

    def plot_spline_fit(self, up_red, down_red, splines, index, save_dir):
        """绘制拟合曲线图片（红色与蓝色曲线并叠加中心点）"""
        plt.figure(figsize=(14, 10))
        plt.title(f"Block {index+1}: Spline Fit Curves")

        # 绘制上游样条拟合曲线和中心点
        if 'up' in splines:
            xs, ys, t0, t1 = splines['up']
            tplt = np.linspace(t0, t1, 200)
            plt.plot(splev(tplt, xs), splev(tplt, ys), 'r-', linewidth=3, label='Upstream Spline')

            # 叠加上游中心点
            if up_red is not None and up_red.size and up_red.ndim == 2 and up_red.shape[0] > 0:
                plt.scatter(
                    up_red[:, 0],
                    up_red[:, 1],
                    c='red',
                    marker='v',  # 下三角箭头
                    s=60,
                    alpha=0.8,
                    edgecolors='darkred',
                    linewidth=1
                )

        # 绘制下游样条拟合曲线和中心点
        if 'down' in splines:
            xs, ys, t0, t1 = splines['down']
            tplt = np.linspace(t0, t1, 200)
            plt.plot(splev(tplt, xs), splev(tplt, ys), 'b-', linewidth=3, label='Downstream Spline')

            # 叠加下游中心点
            if down_red is not None and down_red.size and down_red.ndim == 2 and down_red.shape[0] > 0:
                plt.scatter(
                    down_red[:, 0],
                    down_red[:, 1],
                    c='blue',
                    marker='^',  # 上三角箭头
                    s=60,
                    alpha=0.8,
                    edgecolors='darkblue',
                    linewidth=1
                )

        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.legend()
        plt.tight_layout()

        save_path = os.path.join(save_dir, 'figure4_spline_fit.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"图4已保存至: {save_path}")

    def plot_filtered_scatter(self, up_data, down_data, up_centers, down_centers, save_dir):
        """
        为每个数据块生成聚类和噪点筛选后的散点图

        Args:
            up_data: 上游数据列表
            down_data: 下游数据列表
            up_centers: 上游中心点列表
            down_centers: 下游中心点列表
            save_dir: 保存目录
        """
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # 构建中心点索引映射，支持多中心点区域
        up_center_mapping = self._build_center_mapping(up_centers, len(up_data))
        down_center_mapping = self._build_center_mapping(down_centers, len(down_data))

        for block_idx, (up_block, down_block) in enumerate(zip(up_data, down_data)):
            # 为每个数据块创建独立的文件夹
            block_dir = os.path.join(save_dir, f'block_{block_idx + 1}')
            if not os.path.exists(block_dir):
                os.makedirs(block_dir)

            plt.figure(figsize=(12, 8))
            plt.title(f"数据块 {block_idx + 1} KMeans中心点聚类结果")

            # 绘制上游数据点（红色下三角）
            if isinstance(up_block, np.ndarray) and up_block.size > 0:
                plt.scatter(
                    up_block[:, 0],
                    up_block[:, 1],
                    c='red',
                    marker='v',  # 下三角箭头
                    s=20,
                    alpha=0.7,
                    label=f'上游 ({len(up_block)} 点)'
                )

            # 绘制下游数据点（蓝色上三角）
            if isinstance(down_block, np.ndarray) and down_block.size > 0:
                plt.scatter(
                    down_block[:, 0],
                    down_block[:, 1],
                    c='blue',
                    marker='^',  # 上三角箭头
                    s=20,
                    alpha=0.7,
                    label=f'下游 ({len(down_block)} 点)'
                )

            # 绘制上游KMeans中心点（支持多个中心点）
            if block_idx in up_center_mapping:
                up_centers_for_block = up_center_mapping[block_idx]
                for i, up_center in enumerate(up_centers_for_block):
                    plt.scatter(
                        up_center[0],
                        up_center[1],
                        c='darkred',
                        marker='*',
                        s=200,
                        alpha=1,
                        label=f'上游KMeans中心点{i+1}' if len(up_centers_for_block) > 1 else '上游KMeans中心点'
                    )

            # 绘制下游KMeans中心点（支持多个中心点）
            if block_idx in down_center_mapping:
                down_centers_for_block = down_center_mapping[block_idx]
                for i, down_center in enumerate(down_centers_for_block):
                    plt.scatter(
                        down_center[0],
                        down_center[1],
                        c='darkblue',
                        marker='*',
                        s=200,
                        alpha=1,
                        label=f'下游KMeans中心点{i+1}' if len(down_centers_for_block) > 1 else '下游KMeans中心点'
                    )

            plt.xlabel('经度')
            plt.ylabel('纬度')
            plt.grid(True, linestyle='--', alpha=0.3)
            plt.legend()
            plt.tight_layout()

            save_path = os.path.join(block_dir, 'kmeans_filtered_scatter.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"数据块 {block_idx + 1} 的KMeans散点图已保存至: {save_path}")

            # 在每个数据块文件夹中也保存一份总体散点图
            if block_idx == len(up_data) - 1:  # 在处理最后一个数据块时生成总体图
                self.plot_all_filtered_data(up_data, down_data, up_centers, down_centers, block_dir)

    def plot_all_filtered_data(self, up_data, down_data, up_centers, down_centers, save_dir):
        """
        生成所有数据块合并后的散点图

        Args:
            up_data: 上游数据列表
            down_data: 下游数据列表
            up_centers: 上游中心点列表
            down_centers: 下游中心点列表
            save_dir: 保存目录
        """
        plt.figure(figsize=(16, 10))
        plt.title("所有数据块KMeans中心点聚类结果")

        # 合并所有上游数据点
        total_up_points = 0
        for up_block in up_data:
            if isinstance(up_block, np.ndarray) and up_block.size > 0:
                plt.scatter(
                    up_block[:, 0],
                    up_block[:, 1],
                    c='red',
                    marker='o',
                    s=20,
                    alpha=0.5,
                    label='上游数据点' if total_up_points == 0 else ""
                )
                total_up_points += len(up_block)

        # 合并所有下游数据点
        total_down_points = 0
        for down_block in down_data:
            if isinstance(down_block, np.ndarray) and down_block.size > 0:
                plt.scatter(
                    down_block[:, 0],
                    down_block[:, 1],
                    c='blue',
                    marker='^',
                    s=20,
                    alpha=0.5,
                    label='下游数据点' if total_down_points == 0 else ""
                )
                total_down_points += len(down_block)

        # 绘制所有上游KMeans中心点
        if up_centers:
            up_centers_array = np.array(up_centers)
            plt.scatter(
                up_centers_array[:, 0],
                up_centers_array[:, 1],
                c='darkred',
                marker='*',
                s=200,
                alpha=1,
                label='上游KMeans中心点'
            )

        # 绘制所有下游KMeans中心点
        if down_centers:
            down_centers_array = np.array(down_centers)
            plt.scatter(
                down_centers_array[:, 0],
                down_centers_array[:, 1],
                c='darkblue',
                marker='*',
                s=200,
                alpha=1,
                label='下游KMeans中心点'
            )

        plt.xlabel('经度')
        plt.ylabel('纬度')
        plt.grid(True, linestyle='--', alpha=0.3)

        # 添加数据点统计信息到图例
        legend_elements = [
            Line2D([0], [0], marker='o', color='w', markerfacecolor='red',
                  label=f'上游数据点 ({total_up_points})', markersize=8),
            Line2D([0], [0], marker='^', color='w', markerfacecolor='blue',
                  label=f'下游数据点 ({total_down_points})', markersize=8),
            Line2D([0], [0], marker='*', color='w', markerfacecolor='darkred',
                  label=f'上游KMeans中心点 ({len(up_centers)})', markersize=12),
            Line2D([0], [0], marker='*', color='w', markerfacecolor='darkblue',
                  label=f'下游KMeans中心点 ({len(down_centers)})', markersize=12)
        ]
        plt.legend(handles=legend_elements)

        plt.tight_layout()

        save_path = os.path.join(save_dir, 'all_blocks_kmeans_scatter.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"所有数据的KMeans散点图已保存至: {save_path}")

    def _build_center_mapping(self, centers, num_blocks):
        """
        构建中心点到数据块的映射，支持多中心点区域

        Args:
            centers: 中心点列表
            num_blocks: 数据块数量

        Returns:
            dict: {block_idx: [center1, center2, ...]}
        """
        mapping = {}
        center_idx = 0

        for block_idx in range(num_blocks):
            if center_idx < len(centers):
                # 默认每个区域一个中心点
                mapping[block_idx] = [centers[center_idx]]
                center_idx += 1

                # 检查是否是最后一个区域且还有剩余中心点（多中心点情况）
                if block_idx == num_blocks - 1 and center_idx < len(centers):
                    # 将剩余的中心点都分配给最后一个区域
                    while center_idx < len(centers):
                        mapping[block_idx].append(centers[center_idx])
                        center_idx += 1

        return mapping

    def save_labeled_data(self, up_data, down_data, save_dir):
        """
        保存带标签的数据集到CSV文件

        Args:
            up_data: 上游数据列表
            down_data: 下游数据列表
            save_dir: 保存目录
        """
        if not up_data or not down_data:
            print("数据不完整，无法保存")
            return False

        # 创建保存目录
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # 用于存储所有数据
        all_data_list = []

        # 为每个数据块生成CSV文件
        for block_idx, (up_block, down_block) in enumerate(zip(up_data, down_data)):
            block_dir = os.path.join(save_dir, f'block_{block_idx + 1}')
            if not os.path.exists(block_dir):
                os.makedirs(block_dir)

            # 合并当前数据块的上游和下游数据
            block_data = []

            # 添加上游数据（标签为1）
            if isinstance(up_block, np.ndarray) and up_block.size > 0:
                up_with_label = np.column_stack([up_block, np.ones(len(up_block))])
                block_data.append(up_with_label)

            # 添加下游数据（标签为0）
            if isinstance(down_block, np.ndarray) and down_block.size > 0:
                down_with_label = np.column_stack([down_block, np.zeros(len(down_block))])
                block_data.append(down_with_label)

            if block_data:
                # 合并当前数据块的所有数据
                block_data = np.vstack(block_data)

                # 创建DataFrame
                df = pd.DataFrame(block_data, columns=['经度', '纬度', '航向', '标签'])

                # 保存当前数据块的CSV文件
                csv_path = os.path.join(block_dir, 'kmeans_labeled_data.csv')
                df.to_csv(csv_path, index=False, encoding='utf-8')
                print(f"数据块 {block_idx + 1} 的KMeans标签数据已保存至: {csv_path}")

                # 添加到总数据列表
                all_data_list.append(block_data)

        # 合并所有数据块并保存总CSV文件
        if all_data_list:
            all_data = np.vstack(all_data_list)
            all_df = pd.DataFrame(all_data, columns=['经度', '纬度', '航向', '标签'])
            all_csv_path = os.path.join(save_dir, 'all_kmeans_labeled_data.csv')
            all_df.to_csv(all_csv_path, index=False, encoding='utf-8')
            print(f"所有数据的KMeans标签数据已保存至: {all_csv_path}")

        return True
