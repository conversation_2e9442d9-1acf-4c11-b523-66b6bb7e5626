import pandas as pd
import matplotlib.pyplot as plt
import sys
import os
from pathlib import Path
import glob

# 设置中文字体支持
def setup_chinese_font():
    """设置中文字体，避免字体警告"""
    import matplotlib.font_manager as fm

    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    # 定义候选中文字体列表（按优先级排序）
    chinese_fonts = [
        "SimHei",           # 黑体
        "Microsoft YaHei",  # 微软雅黑
        "SimSun",          # 宋体
        "KaiTi",           # 楷体
        "FangSong",        # 仿宋
        "STHeiti",         # 华文黑体
        "STSong",          # 华文宋体
    ]

    # 找到第一个可用的中文字体
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break

    if selected_font:
        plt.rcParams["font.family"] = [selected_font]
        print(f"✅ 中文字体设置成功: {selected_font}")
    else:
        print("⚠️  未找到中文字体，使用默认字体（可能无法正确显示中文）")

    plt.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题

# 初始化字体设置
setup_chinese_font()


def is_point_in_polygon(lon, lat, polygon_coords):
    """判断点是否在多边形区域内（使用射线法）"""
    n = len(polygon_coords)
    inside = False
    x, y = lon, lat
    for i in range(n):
        j = (i + 1) % n
        xi, yi = polygon_coords[i]
        xj, yj = polygon_coords[j]
        # 处理垂直边情况
        if xi == xj:
            if xi == x and (min(yi, yj) <= y <= max(yi, yj)):
                return True
            continue
        # 射线法判断点是否在多边形内
        if ((yi > y) != (yj > y)):
            x_intersect = (y - yi) * (xj - xi) / (yj - yi) + xi
            if x <= x_intersect:
                inside = not inside
    return inside


def filter_ships_by_area(ships_data, area_coords):
    """筛选出位于指定区域内的船舶数据"""
    filtered_ships = []
    for _, ship in ships_data.iterrows():
        try:
            lon = float(ship[1])  # 第二列（索引1）为经度
            lat = float(ship[2])  # 第三列为纬度（索引2）
        except (ValueError, TypeError):
            continue  # 跳过无法转换为浮点数的行
        if is_point_in_polygon(lon, lat, area_coords):
            filtered_ships.append(ship)
    return pd.DataFrame(filtered_ships)


def main():
    # ====================== 配置参数 ======================
    INPUT_FILE_PATH = r"E:\实验代码备份\ais data\excel_output_长江\长江\ais-data-2024-11-01-1_part2.xlsx"  # 输入文件路径
    OUTPUT_FILE_PATH = r"E:\实验代码备份\ais data\excel_output_长江\切3\筛选结果_2.xlsx"  # 主输出文件路径      
    EXTRACTED_CSV_PATH1 = r"E:\实验代码备份\ais data\excel_output_长江\切3\经纬度提取_2.csv"  # 经纬度提取文件路径、
    EXTRACTED_CSV_PATH2 = r"E:\实验代码备份\ais data\excel_output_长江\切3\经纬度航向提取_2.csv"  # 经纬度+航向提取文件路径、   
    SCATTER_PLOT_PATH = (r"E:\实验代码备份\ais data\excel_output_长江\切3\数据散点图_2.png")  # 散点图路径

    # ====================== 多边形区域坐标 ======================
    area_coordinates = [
        (114.82111666666667, 30.45135),
        (114.81948333333334, 30.47425),
        (114.82335, 30.509783333333335),
        (114.82893333333334, 30.55525),
        (114.82643333333333, 30.578116666666666),
        (114.82008333333333, 30.588066666666666),
        (114.81485, 30.594466666666666),
        (114.79643333333334, 30.606633333333335),
        (114.81395, 30.63395),
        (114.84723333333334, 30.588816666666666),
        (114.84071666666667, 30.556433333333334),
        (114.83831666666667, 30.529983333333334),
        (114.83831666666667, 30.482133333333334),
        (114.84895, 30.452683333333333),
        (114.86955, 30.430666666666667),
        (114.89838333333333, 30.418466666666667),
        (114.93545, 30.4161),
        (114.9818, 30.4152),
        (115.00308333333334, 30.416383333333332),
        (115.04735, 30.40955),
        (115.05456666666667, 30.406266666666667),
        (115.03988333333334, 30.389533333333333),
        (115.01438333333333, 30.4007),
        (114.98856666666667, 30.402183333333333),
        (114.98315, 30.397866666666665),
        (114.95801666666667, 30.398316666666666),
        (114.92926666666666, 30.404716666666666),
        (114.89948333333334, 30.40895),
        (114.8486, 30.42375),
        (114.83331666666666, 30.43075),
        (114.8267, 30.43573333333333)
    ]
    print("开始读取数据...")
    try:
        # 读取Excel文件
        ships_df = pd.read_excel(INPUT_FILE_PATH)
        print(f"数据读取完成，共 {len(ships_df)} 条记录")
    except Exception as e:
        print(f"读取文件失败: {e}")
        sys.exit(1)

    print("正在筛选区域内数据...")
    # 筛选区域内的船舶
    filtered_df = filter_ships_by_area(ships_df, area_coordinates)
    print(f"筛选完成，区域内数据: {len(filtered_df)} 条")

    # ====================== 保存筛选结果 ======================
    print("正在保存筛选结果...")
    try:
        filtered_df.to_excel(OUTPUT_FILE_PATH, index=False)
        print(f"筛选结果已保存至: {OUTPUT_FILE_PATH}")
    except Exception as e:
        print(f"保存Excel失败: {e}")
        sys.exit(1)

    # ====================== 提取并保存经纬度数据 ======================
    try:
        # 提取经纬度数据（假设第2列是经度，第3列是纬度）
        extracted_data = filtered_df.iloc[:, [1, 2]]  # 提取第2列和第3列
        extracted_data.to_csv(EXTRACTED_CSV_PATH1, index=False)
        print(f"经纬度数据已保存至: {EXTRACTED_CSV_PATH1}")

        # 提取经纬度和航向数据（假设第2列是经度，第3列是纬度，第8列是航向）
        extracted_data = filtered_df.iloc[:, [1, 2, 7]]  # 提取第2、3、8列
        extracted_data.to_csv(EXTRACTED_CSV_PATH2, index=False)
        print(f"经纬度、航向数据已保存至: {EXTRACTED_CSV_PATH2}")
    except Exception as e:
        print(f"保存CSV失败: {e}")
        sys.exit(1)

    # ====================== 生成数据散点图 ======================
    try:
        plt.figure(figsize=(12, 8))

        if not filtered_df.empty:
            # 提取经纬度数据
            lons = []
            lats = []

            for _, row in filtered_df.iterrows():
                try:
                    lon = float(row[1])  # 经度列
                    lat = float(row[2])  # 纬度列
                    lons.append(lon)
                    lats.append(lat)
                except (ValueError, TypeError):
                    continue

            if lons and lats:
                # 创建散点图
                plt.scatter(lons, lats, c='blue', s=20, alpha=0.6, edgecolors='navy', linewidth=0.5)

                # 设置图表标题和标签
                plt.title(f'AIS数据散点图 (共 {len(lons)} 个数据点)', fontsize=14, fontweight='bold')
                plt.xlabel('经度 (Longitude)', fontsize=12)
                plt.ylabel('纬度 (Latitude)', fontsize=12)

                # 添加网格
                plt.grid(True, alpha=0.3)

                # 设置坐标轴范围，留出一些边距
                lon_margin = (max(lons) - min(lons)) * 0.05
                lat_margin = (max(lats) - min(lats)) * 0.05
                plt.xlim(min(lons) - lon_margin, max(lons) + lon_margin)
                plt.ylim(min(lats) - lat_margin, max(lats) + lat_margin)

                # 添加统计信息文本框
                stats_text = f'数据点数量: {len(lons)}\n'
                stats_text += f'经度范围: {min(lons):.4f} ~ {max(lons):.4f}\n'
                stats_text += f'纬度范围: {min(lats):.4f} ~ {max(lats):.4f}'

                plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

                # 调整布局
                plt.tight_layout()

                # 保存图片
                plt.savefig(SCATTER_PLOT_PATH, dpi=300, bbox_inches='tight')
                plt.close()

                print(f"散点图已保存为 {SCATTER_PLOT_PATH}")
            else:
                print("警告: 没有有效的经纬度数据用于绘制散点图")
        else:
            print("警告: 筛选后的数据为空，无法生成散点图")

    except Exception as e:
        print(f"警告: 散点图生成过程中发生异常: {e}")
        if 'plt' in locals():
            plt.close()


if __name__ == "__main__":
    main()
