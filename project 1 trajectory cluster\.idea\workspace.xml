<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a7432468-da2e-4588-9f12-f0e1011bcd05" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2uRRNbPXqWp0vq2sycC0I6e8XQ5" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.111.executor&quot;: &quot;Run&quot;,
    &quot;Python.123-claude.executor&quot;: &quot;Run&quot;,
    &quot;Python.123.executor&quot;: &quot;Run&quot;,
    &quot;Python.555.executor&quot;: &quot;Run&quot;,
    &quot;Python.AgglomerativeClustering.executor&quot;: &quot;Run&quot;,
    &quot;Python.Comb_main.executor&quot;: &quot;Run&quot;,
    &quot;Python.Comb_main_1.executor&quot;: &quot;Run&quot;,
    &quot;Python.Multi-clustering02.executor&quot;: &quot;Run&quot;,
    &quot;Python.Multi-clustering2 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.Multi-clustering2-2.executor&quot;: &quot;Run&quot;,
    &quot;Python.Multi-clustering2.executor&quot;: &quot;Run&quot;,
    &quot;Python.Multi-clustering3.executor&quot;: &quot;Run&quot;,
    &quot;Python.SpectralClustering.executor&quot;: &quot;Run&quot;,
    &quot;Python.aisdata_process.executor&quot;: &quot;Run&quot;,
    &quot;Python.ais数据分布展示 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.dataprocess.executor&quot;: &quot;Run&quot;,
    &quot;Python.dbsacn.executor&quot;: &quot;Run&quot;,
    &quot;Python.dbscan.executor&quot;: &quot;Run&quot;,
    &quot;Python.dbscan1 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.kmeans (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.kmeans.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.main02.executor&quot;: &quot;Run&quot;,
    &quot;Python.main03.executor&quot;: &quot;Run&quot;,
    &quot;Python.main04.executor&quot;: &quot;Run&quot;,
    &quot;Python.main修改.executor&quot;: &quot;Run&quot;,
    &quot;Python.sheet表汇总 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.切分区域1数据筛选 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.切分区域数据筛选_船讯网 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.区域数据筛选.executor&quot;: &quot;Run&quot;,
    &quot;Python.原始数据展示.executor&quot;: &quot;Run&quot;,
    &quot;Python.数据切分.executor&quot;: &quot;Run&quot;,
    &quot;Python.数据提取.executor&quot;: &quot;Run&quot;,
    &quot;Python.航道ais数据提取_mapshaper.executor&quot;: &quot;Run&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/014PythonDatas&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;Code Search&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\实验代码备份\project 1 trajectory cluster\2025" />
    </key>
  </component>
  <component name="RunManager" selected="Python.Multi-clustering2">
    <configuration name="数据切分" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="project 1 trajectory cluster" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/2025" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/2025/数据切分.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="123" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="project 1 trajectory cluster" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/2025" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/2025/123.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Multi-clustering2" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="project 1 trajectory cluster" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/2025" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/2025/Multi-clustering2.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Multi-clustering3" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="project 1 trajectory cluster" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/2025" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/2025/Multi-clustering3.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ais数据分布展示 (1)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="project 1 trajectory cluster" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/2025" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/2025/ais数据分布展示.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.Multi-clustering2" />
        <item itemvalue="Python.Multi-clustering3" />
        <item itemvalue="Python.数据切分" />
        <item itemvalue="Python.123" />
        <item itemvalue="Python.ais数据分布展示 (1)" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-91d5c284f522-JavaScript-PY-241.15989.155" />
        <option value="bundled-python-sdk-babbdf50b680-7c6932dee5e4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.15989.155" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a7432468-da2e-4588-9f12-f0e1011bcd05" name="Default Changelist" comment="" />
      <created>1742209360349</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1742209360349</updated>
      <workItem from="1745975094737" duration="8464000" />
      <workItem from="1746077968678" duration="89000" />
      <workItem from="1746078260539" duration="2893000" />
      <workItem from="1746594720065" duration="4850000" />
      <workItem from="1746632029120" duration="68000" />
      <workItem from="1746632736943" duration="49000" />
      <workItem from="1746683223983" duration="2922000" />
      <workItem from="1746686928102" duration="837000" />
      <workItem from="1747131494735" duration="3899000" />
      <workItem from="1747155111908" duration="917000" />
      <workItem from="1747653344333" duration="846000" />
      <workItem from="1747734703749" duration="5961000" />
      <workItem from="1747821278588" duration="653000" />
      <workItem from="1747824242282" duration="6752000" />
      <workItem from="1747895179779" duration="3834000" />
      <workItem from="1748412186894" duration="3387000" />
      <workItem from="1748415623022" duration="6663000" />
      <workItem from="1748504285009" duration="13826000" />
      <workItem from="1748521380901" duration="1303000" />
      <workItem from="1748579618660" duration="4817000" />
      <workItem from="1748611738835" duration="1851000" />
      <workItem from="1748838397168" duration="4693000" />
      <workItem from="1748843990108" duration="36000" />
      <workItem from="1748847784530" duration="10187000" />
      <workItem from="1749013994608" duration="9968000" />
      <workItem from="1749101799027" duration="4707000" />
      <workItem from="1749113628701" duration="1895000" />
      <workItem from="1749444239676" duration="6000" />
      <workItem from="1749444271432" duration="1655000" />
      <workItem from="1749620225817" duration="16000" />
      <workItem from="1749625107440" duration="3934000" />
      <workItem from="1749790856650" duration="1316000" />
      <workItem from="1749809824505" duration="2400000" />
      <workItem from="1749965337711" duration="5532000" />
      <workItem from="1749972595554" duration="2019000" />
      <workItem from="1750063803976" duration="2685000" />
      <workItem from="1750148733204" duration="1370000" />
      <workItem from="1750231574907" duration="1229000" />
      <workItem from="1750235250300" duration="37000" />
      <workItem from="1751343349893" duration="1555000" />
      <workItem from="1751347686922" duration="2983000" />
      <workItem from="1751366686039" duration="977000" />
      <workItem from="1751870251975" duration="7000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/main02.py</url>
          <line>216</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/2025/dbscan.py</url>
          <line>15</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/2025/dbscan.py</url>
          <line>19</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/2025/dbscan.py</url>
          <line>20</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/2025/dbscan.py</url>
          <line>21</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$Multi_clustering2.coverage" NAME="Multi-clustering2 Coverage Results" MODIFIED="1748522628724" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/Multi_clustering3_py$.coverage" NAME="数据切分 Coverage Results" MODIFIED="1751343468636" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$kmeans.coverage" NAME="kmeans Coverage Results" MODIFIED="1747896564619" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$1__1_.coverage" NAME="切分区域1数据筛选 (1) Coverage Results" MODIFIED="1748522179041" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$aisdata_process.coverage" NAME="aisdata_process Coverage Results" MODIFIED="1749444291648" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$Comb_main_1.coverage" NAME="Comb_main_1 Coverage Results" MODIFIED="1748412720213" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$Multi_clustering02.coverage" NAME="Multi-clustering02 Coverage Results" MODIFIED="1747740260672" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$SpectralClustering.coverage" NAME="SpectralClustering Coverage Results" MODIFIED="1746684700102" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$123_claude.coverage" NAME="123-claude Coverage Results" MODIFIED="1749791907701" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$Multi_clustering3.coverage" NAME="Multi-clustering3 Coverage Results" MODIFIED="1750065301001" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$dbscan.coverage" NAME="dbscan Coverage Results" MODIFIED="1746683604715" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$ais_mapshaper.coverage" NAME="航道ais数据提取_mapshaper Coverage Results" MODIFIED="1748581801049" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$kmeans__1_.coverage" NAME="kmeans (1) Coverage Results" MODIFIED="1746685262082" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$ais__1_.coverage" NAME="ais数据分布展示 (1) Coverage Results" MODIFIED="1749970599704" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/Multi_clustering3_py$Multi_clustering2.coverage" NAME="Multi-clustering2 Coverage Results" MODIFIED="1751367029755" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$___1_.coverage" NAME="切分区域数据筛选_船讯网 (1) Coverage Results" MODIFIED="1749969719646" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$dataprocess.coverage" NAME="dataprocess Coverage Results" MODIFIED="1749104332052" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$main03.coverage" NAME="main03 Coverage Results" MODIFIED="1748416387855" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$111.coverage" NAME="111 Coverage Results" MODIFIED="1746630761403" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$dbscan1__1_.coverage" NAME="dbscan1 (1) Coverage Results" MODIFIED="1746685271217" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$Comb_main.coverage" NAME="Comb_main Coverage Results" MODIFIED="1748416405192" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$AgglomerativeClustering.coverage" NAME="AgglomerativeClustering Coverage Results" MODIFIED="1746685114996" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$main04.coverage" NAME="main04 Coverage Results" MODIFIED="1748416348273" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$sheet__1_.coverage" NAME="sheet表汇总 (1) Coverage Results" MODIFIED="1749970500427" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$main.coverage" NAME="main Coverage Results" MODIFIED="1746687052881" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$123.coverage" NAME="123 Coverage Results" MODIFIED="1749974586404" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$555.coverage" NAME="555 Coverage Results" MODIFIED="1748417552365" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$Multi_clustering2_2.coverage" NAME="Multi-clustering2-2 Coverage Results" MODIFIED="1748521523455" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/Multi_clustering3_py$Multi_clustering3.coverage" NAME="Multi-clustering3 Coverage Results" MODIFIED="1751366835818" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$dbsacn.coverage" NAME="dbsacn Coverage Results" MODIFIED="1746684143390" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$.coverage" NAME="数据切分 Coverage Results" MODIFIED="1750064866299" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$Multi_clustering2__1_.coverage" NAME="Multi-clustering2 (1) Coverage Results" MODIFIED="1749104785039" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/2025" />
    <SUITE FILE_PATH="coverage/project_1_trajectory_cluster$main02.coverage" NAME="main02 Coverage Results" MODIFIED="1746630345096" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>