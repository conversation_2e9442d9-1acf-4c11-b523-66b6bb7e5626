#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的NaN值清理脚本
解决DBSCAN "Input X contains NaN" 错误
"""

import pandas as pd
import numpy as np

def clean_data():
    """清理数据中的NaN值"""
    # 输入和输出文件路径
    input_file = r"E:\实验代码备份\ais data\excel_output_长江\切3\筛选结果_经纬度航向.csv"
    output_file = r"E:\实验代码备份\ais data\excel_output_长江\切3\筛选结果_经纬度航向_cleaned.csv"
    
    print("读取数据...")
    # 读取数据
    df = pd.read_csv(input_file)
    print(f"原始数据: {len(df)} 行")
    
    # 检查NaN值
    nan_count = df.isnull().sum().sum()
    print(f"总NaN值: {nan_count}")
    
    # 删除包含NaN的行
    df_clean = df.dropna()
    print(f"清理后数据: {len(df_clean)} 行")
    print(f"删除了: {len(df) - len(df_clean)} 行")
    
    # 保存清理后的数据
    df_clean.to_csv(output_file, index=False)
    print(f"已保存到: {output_file}")
    
    return output_file

if __name__ == "__main__":
    clean_data()
    print("完成！")
