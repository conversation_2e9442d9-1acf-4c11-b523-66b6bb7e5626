#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AIS数据可视化脚本
"""

import os
import sys

def test_file_existence():
    """测试输入文件是否存在"""
    print("🔍 检查输入文件...")
    
    input_files = [
        r"E:\实验代码备份\ais data\excel_output_长江\切2\筛选结果_经纬度航向.csv",
        r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_经纬度航向.csv",
        r"E:\实验代码备份\ais data\excel_output_长江\切3\筛选结果_经纬度航向.csv"
    ]
    
    existing_files = []
    missing_files = []
    
    for file_path in input_files:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / 1024 / 1024
            print(f"✅ {os.path.basename(file_path)} ({size_mb:.2f} MB)")
            existing_files.append(file_path)
        else:
            print(f"❌ {os.path.basename(file_path)} - 文件不存在")
            missing_files.append(file_path)
    
    print(f"\n📊 文件检查结果:")
    print(f"   - 存在的文件: {len(existing_files)}/3")
    print(f"   - 缺失的文件: {len(missing_files)}/3")
    
    if missing_files:
        print(f"\n⚠️  缺失文件列表:")
        for file_path in missing_files:
            print(f"   - {file_path}")
    
    return len(existing_files) > 0

def test_output_directory():
    """测试输出目录是否可写"""
    print(f"\n📁 检查输出目录...")
    
    output_dir = r"E:\实验代码备份\ais data\excel_output_长江"
    
    if os.path.exists(output_dir):
        if os.access(output_dir, os.W_OK):
            print(f"✅ 输出目录可写: {output_dir}")
            return True
        else:
            print(f"❌ 输出目录不可写: {output_dir}")
            return False
    else:
        try:
            os.makedirs(output_dir, exist_ok=True)
            print(f"✅ 输出目录已创建: {output_dir}")
            return True
        except Exception as e:
            print(f"❌ 无法创建输出目录: {e}")
            return False

def test_dependencies():
    """测试依赖包是否安装"""
    print(f"\n📦 检查依赖包...")
    
    required_packages = ['pandas', 'matplotlib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 安装缺失的包:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🧪 AIS数据可视化脚本测试")
    print("=" * 50)
    
    # 测试各项条件
    files_ok = test_file_existence()
    dir_ok = test_output_directory()
    deps_ok = test_dependencies()
    
    print(f"\n📋 测试结果总结:")
    print(f"   - 输入文件: {'✅' if files_ok else '❌'}")
    print(f"   - 输出目录: {'✅' if dir_ok else '❌'}")
    print(f"   - 依赖包: {'✅' if deps_ok else '❌'}")
    
    if files_ok and dir_ok and deps_ok:
        print(f"\n🎉 所有测试通过！可以运行AIS数据可视化脚本")
        return True
    else:
        print(f"\n⚠️  存在问题，请解决后再运行主脚本")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
