import pandas as pd
import matplotlib.pyplot as plt
import sys
import os
from pathlib import Path

# 设置中文字体支持
plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]
plt.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题


def is_point_in_polygon(lon, lat, polygon_coords):
    """判断点是否在多边形区域内（使用射线法）"""
    n = len(polygon_coords)
    inside = False
    x, y = lon, lat
    for i in range(n):
        j = (i + 1) % n
        xi, yi = polygon_coords[i]
        xj, yj = polygon_coords[j]
        # 处理垂直边情况
        if xi == xj:
            if xi == x and (min(yi, yj) <= y <= max(yi, yj)):
                return True
            continue
        # 射线法判断点是否在多边形内
        if ((yi > y) != (yj > y)):
            x_intersect = (y - yi) * (xj - xi) / (yj - yi) + xi
            if x <= x_intersect:
                inside = not inside
    return inside


def filter_data_by_area(data_df, area_coords):
    """筛选出位于指定区域内的数据"""
    filtered_data = []
    for _, row in data_df.iterrows():
        try:
            lon = float(row.iloc[0])  # 第一列为经度
            lat = float(row.iloc[1])  # 第二列为纬度
        except (ValueError, TypeError):
            continue  # 跳过无法转换为浮点数的行
        if is_point_in_polygon(lon, lat, area_coords):
            filtered_data.append(row)
    return pd.DataFrame(filtered_data)


def create_scatter_plot(filtered_df, save_path):
    """创建筛选后数据的散点图"""
    try:
        plt.figure(figsize=(12, 8))
        
        if not filtered_df.empty:
            # 提取经纬度数据
            lons = []
            lats = []
            
            for _, row in filtered_df.iterrows():
                try:
                    lon = float(row.iloc[0])  # 经度列
                    lat = float(row.iloc[1])  # 纬度列
                    lons.append(lon)
                    lats.append(lat)
                except (ValueError, TypeError):
                    continue
            
            if lons and lats:
                # 创建散点图
                plt.scatter(lons, lats, c='blue', s=20, alpha=0.6, edgecolors='navy', linewidth=0.5)
                
                # 设置图表标题和标签
                plt.title(f'筛选后数据散点图 (共 {len(lons)} 个数据点)', fontsize=14, fontweight='bold')
                plt.xlabel('经度 (Longitude)', fontsize=12)
                plt.ylabel('纬度 (Latitude)', fontsize=12)
                
                # 添加网格
                plt.grid(True, alpha=0.3)
                
                # 设置坐标轴范围，留出一些边距
                lon_margin = (max(lons) - min(lons)) * 0.05
                lat_margin = (max(lats) - min(lats)) * 0.05
                plt.xlim(min(lons) - lon_margin, max(lons) + lon_margin)
                plt.ylim(min(lats) - lat_margin, max(lats) + lat_margin)
                
                # 添加统计信息文本框
                stats_text = f'数据点数量: {len(lons)}\n'
                stats_text += f'经度范围: {min(lons):.4f} ~ {max(lons):.4f}\n'
                stats_text += f'纬度范围: {min(lats):.4f} ~ {max(lats):.4f}'
                
                plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
                
                # 调整布局
                plt.tight_layout()
                
                # 保存图片
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                print(f"散点图已保存为 {save_path}")
            else:
                print("警告: 没有有效的经纬度数据用于绘制散点图")
        else:
            print("警告: 筛选后的数据为空，无法生成散点图")
            
    except Exception as e:
        print(f"警告: 散点图生成过程中发生异常: {e}")
        if 'plt' in locals():
            plt.close()


def main():
    # ====================== 配置参数 ======================
    INPUT_FILE_PATH = r"E:\实验代码备份\ais data\excel_output_长江\切3\合并后的经纬度航向数据.csv"  # 输入CSV文件路径
    OUTPUT_FILE_PATH = r"E:\实验代码备份\ais data\excel_output_长江\切3\筛选结果_经纬度航向.csv"  # 输出CSV文件路径
    SCATTER_PLOT_PATH = r"E:\实验代码备份\ais data\excel_output_长江\切3\筛选后数据散点图.png"  # 散点图路径

    # ====================== 多边形区域坐标 ======================
    area_coordinates = [
        (114.82111666666667, 30.45135),
        (114.81948333333334, 30.47425),
        (114.82335, 30.509783333333335),
        (114.82893333333334, 30.55525),
        (114.82643333333333, 30.578116666666666),
        (114.82008333333333, 30.588066666666666),
        (114.81485, 30.594466666666666),
        (114.79643333333334, 30.606633333333335),
        (114.81395, 30.63395),
        (114.84723333333334, 30.588816666666666),
        (114.84071666666667, 30.556433333333334),
        (114.83831666666667, 30.529983333333334),
        (114.83831666666667, 30.482133333333334),
        (114.84895, 30.452683333333333),
        (114.86955, 30.430666666666667),
        (114.89838333333333, 30.418466666666667),
        (114.93545, 30.4161),
        (114.9818, 30.4152),
        (115.00308333333334, 30.416383333333332),
        (115.04735, 30.40955),
        (115.05456666666667, 30.406266666666667),
        (115.03988333333334, 30.389533333333333),
        (115.01438333333333, 30.4007),
        (114.98856666666667, 30.402183333333333),
        (114.98315, 30.397866666666665),
        (114.95801666666667, 30.398316666666666),
        (114.92926666666666, 30.404716666666666),
        (114.89948333333334, 30.40895),
        (114.8486, 30.42375),
        (114.83331666666666, 30.43075),
        (114.8267, 30.43573333333333)
    ]

    print("开始读取CSV数据...")
    try:
        # 读取CSV文件
        data_df = pd.read_csv(INPUT_FILE_PATH)
        print(f"数据读取完成，共 {len(data_df)} 条记录")
        print(f"数据列: {list(data_df.columns)}")
    except Exception as e:
        print(f"读取文件失败: {e}")
        sys.exit(1)

    print("正在筛选区域内数据...")
    # 筛选区域内的数据
    filtered_df = filter_data_by_area(data_df, area_coordinates)
    print(f"筛选完成，区域内数据: {len(filtered_df)} 条")

    # ====================== 保存筛选结果 ======================
    print("正在保存筛选结果...")
    try:
        # 确保输出目录存在
        output_path = Path(OUTPUT_FILE_PATH)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        filtered_df.to_csv(OUTPUT_FILE_PATH, index=False, encoding='utf-8-sig')
        print(f"筛选结果已保存至: {OUTPUT_FILE_PATH}")
        
        # 显示筛选结果统计
        if not filtered_df.empty:
            print(f"筛选结果统计:")
            print(f"  - 总记录数: {len(filtered_df)}")
            if len(filtered_df.columns) >= 2:
                print(f"  - 经度范围: {filtered_df.iloc[:, 0].min():.6f} ~ {filtered_df.iloc[:, 0].max():.6f}")
                print(f"  - 纬度范围: {filtered_df.iloc[:, 1].min():.6f} ~ {filtered_df.iloc[:, 1].max():.6f}")
            if len(filtered_df.columns) >= 3:
                print(f"  - 航向范围: {filtered_df.iloc[:, 2].min():.2f} ~ {filtered_df.iloc[:, 2].max():.2f}")
        
    except Exception as e:
        print(f"保存CSV失败: {e}")
        sys.exit(1)

    # ====================== 生成数据散点图 ======================
    try:
        create_scatter_plot(filtered_df, SCATTER_PLOT_PATH)
    except Exception as e:
        print(f"警告: 散点图生成过程中发生异常: {e}")

    # 程序完成提示
    print("\n" + "=" * 60)
    print("🎉 数据筛选处理完成！")
    print(f"📄 筛选结果: {Path(OUTPUT_FILE_PATH).name}")
    print(f"📊 散点图: {Path(SCATTER_PLOT_PATH).name}")
    print(f"📁 保存位置: {Path(OUTPUT_FILE_PATH).parent}")
    print("=" * 60)


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ 程序执行过程中发生错误: {e}")
        print("请检查输入文件路径和数据格式是否正确。")
    
    # 等待用户按键退出
    input("\n按回车键退出...")
