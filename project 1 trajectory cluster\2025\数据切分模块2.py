import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import shutil
from pathlib import Path

# 配置中文字体，按优先级尝试不同字体
try:
    # Windows系统常用字体
    plt.rcParams["font.family"] = ["Microsoft YaHei", "SimHei", "SimSun", "KaiTi", "FangSong"]
except:
    # 如果上述字体都不可用，使用默认字体
    plt.rcParams["font.family"] = ["DejaVu Sans"]
    
plt.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题

# 验证字体设置
import matplotlib.font_manager as fm
available_fonts = [f.name for f in fm.fontManager.ttflist]
chinese_fonts = [font for font in ["Microsoft YaHei", "SimHei", "SimSun", "KaiTi"] if font in available_fonts]
if chinese_fonts:
    plt.rcParams["font.family"] = [chinese_fonts[0]]
    print(f"✅ 使用中文字体: {chinese_fonts[0]}")
else:
    print("⚠️  未找到中文字体，使用默认字体（中文可能显示为方块）")


def read_data(file_path, longitude_col_index=0, latitude_col_index=1):
    """
    读取包含经纬度数据的CSV文件。
    使用列索引而不是列名来确保正确读取经纬度数据。
    
    Args:
        file_path: CSV文件路径
        longitude_col_index: 经度列的索引（默认为0，即第一列）
        latitude_col_index: 纬度列的索引（默认为1，即第二列）
    """
    df = pd.read_csv(file_path)
    
    # 验证列数
    if len(df.columns) < max(longitude_col_index, latitude_col_index) + 1:
        raise ValueError(
            f"文件列数不足，至少需要{max(longitude_col_index, latitude_col_index) + 1}列，实际只有{len(df.columns)}列")
    
    # 提取指定列作为经纬度，并重命名列
    longitude = df.iloc[:, longitude_col_index]  # 指定列: 经度
    latitude = df.iloc[:, latitude_col_index]   # 指定列: 纬度
    
    # 创建新的DataFrame，使用标准列名
    result_df = pd.DataFrame({
        'longitude': longitude,
        'latitude': latitude
    })
    
    # 如果有第三列（航向），也包含进来
    if len(df.columns) > 2:
        result_df['heading'] = df.iloc[:, 2]
    
    # 数据预处理：移除无效值
    valid_mask = result_df['longitude'].notna() & result_df['latitude'].notna()
    result_df = result_df[valid_mask].reset_index(drop=True)
    
    print(f"✅ 数据读取完成，原始记录: {len(df)}，有效记录: {len(result_df)}")
    print(f"📊 经度范围: {result_df['longitude'].min():.6f} ~ {result_df['longitude'].max():.6f}")
    print(f"📊 纬度范围: {result_df['latitude'].min():.6f} ~ {result_df['latitude'].max():.6f}")
    
    return result_df


def get_bounds(df):
    """
    获取经纬度的最大最小值。
    """
    lat_max = df['latitude'].max()
    lat_min = df['latitude'].min()
    lon_max = df['longitude'].max()
    lon_min = df['longitude'].min()
    return lat_max, lat_min, lon_max, lon_min


def construct_rectangle(lat_max, lat_min, lon_max, lon_min):
    """
    构建矩形区域并计算高度和宽度。
    """
    height = lat_max - lat_min
    width = lon_max - lon_min
    return height, width


def determine_start_corner(df, lat_max, lon_min, lon_max, lat_step, lon_step):
    """
    判断从左上角或右上角开始划分区域。
    """
    # 左上角区域
    lat_upper = lat_max
    lat_lower = lat_max - lat_step
    lon_left = lon_min
    lon_right = lon_min + lon_step
    data_left = df[(df['latitude'] <= lat_upper) & (df['latitude'] >= lat_lower) &
                   (df['longitude'] >= lon_left) & (df['longitude'] <= lon_right)]

    # 右上角区域
    lon_left_r = lon_max - lon_step
    lon_right_r = lon_max
    data_right = df[(df['latitude'] <= lat_upper) & (df['latitude'] >= lat_lower) &
                    (df['longitude'] >= lon_left_r) & (df['longitude'] <= lon_right_r)]

    if not data_left.empty:
        return 'left'
    elif not data_right.empty:
        return 'right'
    else:
        return None


def calculate_dynamic_regions(target_segments, custom_lengths=None):
    """
    根据自定义段落宽度计算动态调整后的区域配置。
    
    Args:
        target_segments: 目标切分段落数量（总宽度单位）
        custom_lengths: 自定义段落宽度字典，格式为 {segment_index: width_multiplier}
                       支持负数索引，例如 {0: 2, -1: 2} 表示第1段和最后1段为2倍宽度
    
    Returns:
        tuple: (实际段落配置列表, 实际段落数量, 总宽度单位)
    """
    if custom_lengths is None:
        custom_lengths = {}
    
    # 处理负数索引（-1表示最后一段）
    normalized_custom = {}
    for key, value in custom_lengths.items():
        if key < 0:
            # 负数索引转换为正数索引（-1 -> 最后一段的索引）
            actual_key = target_segments + key + 1
        else:
            # 0-based转换为1-based
            actual_key = key + 1 if key >= 0 else key
        normalized_custom[actual_key] = value
    
    # 计算总消耗的宽度单位
    total_width_consumed = 0
    for i in range(1, target_segments + 1):
        width_multiplier = normalized_custom.get(i, 1.0)
        total_width_consumed += width_multiplier
    
    # 计算实际生成的段落数量
    # 如果某段宽度>1，则会"吃掉"后续的段落位置
    actual_segments = []
    current_position = 1
    consumed_width = 0
    
    while current_position <= target_segments and consumed_width < target_segments:
        width_multiplier = normalized_custom.get(current_position, 1.0)
        
        # 检查是否超出总宽度限制
        if consumed_width + width_multiplier > target_segments:
            # 调整最后一段的宽度以适应剩余空间
            width_multiplier = target_segments - consumed_width
        
        if width_multiplier > 0:
            actual_segments.append({
                'segment_index': current_position,
                'width_multiplier': width_multiplier,
                'start_position': consumed_width,
                'end_position': consumed_width + width_multiplier
            })
            
            consumed_width += width_multiplier
            current_position += 1
        else:
            break
    
    return actual_segments, len(actual_segments), target_segments


def partition_data(file_path, target_segments, custom_lengths=None, longitude_col_index=0, latitude_col_index=1):
    """
    数据切分主函数 - 为聚类分析模块提供的接口
    
    Args:
        file_path: 数据文件路径
        target_segments: 目标切分段落数量
        custom_lengths: 自定义段落宽度字典，格式为 {segment_index: width_multiplier}
        longitude_col_index: 经度列索引
        latitude_col_index: 纬度列索引
    
    Returns:
        list: 切分后的数据列表，每个元素是一个DataFrame
    """
    print(f"🔄 开始数据切分...")
    print(f"   - 目标段落数量: {target_segments}")
    if custom_lengths:
        print(f"   - 自定义段落宽度: {custom_lengths}")
    
    # 读取数据
    df = read_data(file_path, longitude_col_index, latitude_col_index)
    
    # 获取边界和矩形尺寸
    lat_max, lat_min, lon_max, lon_min = get_bounds(df)
    height, width = construct_rectangle(lat_max, lat_min, lon_max, lon_min)
    
    # 判断起始角（使用临时步长）
    temp_lat_step = height / target_segments
    temp_lon_step = width / target_segments
    start_corner = determine_start_corner(df, lat_max, lon_min, lon_max, temp_lat_step, temp_lon_step)
    
    if start_corner is None:
        print("❌ 无法在左上角或右上角找到数据，使用默认切分策略")
        start_corner = 'right'  # 默认使用右上角
    
    start_direction = "左上角" if start_corner == "left" else "右上角"
    print(f"🎯 切分起始方向: {start_direction}")
    
    # 执行动态段落划分
    data_list = divide_and_process_segments(df, lat_max, lat_min, lon_max, lon_min, 
                                          target_segments, start_corner, custom_lengths)
    
    print(f"✅ 数据切分完成，生成 {len(data_list)} 个数据段落")
    
    return data_list


def divide_and_process_segments(df, lat_max, lat_min, lon_max, lon_min, target_segments, start_corner, custom_lengths=None):
    """
    执行动态段落划分的核心函数
    使用累积矩形 + 差集算法，确保数据不重复不遗漏
    """
    if custom_lengths is None:
        custom_lengths = {}

    # 计算动态段落配置
    segment_configs, actual_segments, total_width_units = calculate_dynamic_regions(target_segments, custom_lengths)

    # 计算基础步长（基于总宽度单位）
    base_lat_step = (lat_max - lat_min) / total_width_units
    base_lon_step = (lon_max - lon_min) / total_width_units

    # 使用累积矩形 + 差集算法
    collected_data = pd.DataFrame()
    data_list = []

    current_lat_offset = 0
    current_lon_offset = 0

    for config in segment_configs:
        segment_idx = config['segment_index']
        width_multiplier = config['width_multiplier']

        # 计算当前段落的步长
        segment_lat_step = base_lat_step * width_multiplier
        segment_lon_step = base_lon_step * width_multiplier

        # 更新累积偏移
        current_lat_offset += segment_lat_step
        current_lon_offset += segment_lon_step

        # 计算累积矩形边界（从起始点到当前位置）
        if start_corner == 'left':
            lat_upper = lat_max
            lat_lower = lat_max - current_lat_offset
            lon_left = lon_min
            lon_right = lon_min + current_lon_offset
        else:
            lat_upper = lat_max
            lat_lower = lat_max - current_lat_offset
            lon_left = lon_max - current_lon_offset
            lon_right = lon_max

        # 确保边界不超出数据范围
        lat_lower = max(lat_lower, lat_min)
        lon_left = max(lon_left, lon_min)
        lon_right = min(lon_right, lon_max)

        # 获取累积矩形内的所有数据
        region_data = df[(df['latitude'] <= lat_upper) & (df['latitude'] >= lat_lower) &
                         (df['longitude'] >= lon_left) & (df['longitude'] <= lon_right)]

        # 去除已收集的数据（差集）- 这是关键步骤！
        new_data = region_data[~region_data.index.isin(collected_data.index)]
        collected_data = pd.concat([collected_data, new_data])
        data_list.append(new_data)

        print(f"   段落 {segment_idx}: {width_multiplier}x宽度, 数据点: {len(new_data)}")

    return data_list
