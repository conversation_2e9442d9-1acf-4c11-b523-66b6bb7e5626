import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans, DBSCAN
from spectralcluster import SpectralClusterer
import os
from scipy.interpolate import splrep, splev
import haversine as hs
from 数据切分模块 import partition_data as dp_partition_data

# 设置GUI后端
import matplotlib

# 设置支持中文的字体
matplotlib.rcParams['font.family'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

matplotlib.use('TkAgg')


class GeoDataProcessor:
    """地理空间数据处理与聚类分析管道（三维：经纬度+航向）- 使用KMeans中心点选取"""

    def __init__(self, config):
        """初始化处理器，使用配置字典设置参数"""
        self.config = config
        self.orig_input = None
        self.labels = None
        self.up_data = None
        self.down_data = None
        self.up_centers = None
        self.down_centers = None
        self.up_red = None
        self.down_red = None
        self.splines = {}

    def read_and_partition_data(self):
        """读取数据文件并进行切分（三维：经度、纬度、航向）"""
        try:
            # 使用数据切分模块读取和切分数据
            data_list = dp_partition_data(self.config['file_path'], self.config['partition_count'])

            # 初始化存储切分数据的列表
            self.orig_input = []

            for data in data_list:
                if data is not None and data.shape[1] >= 3:
                    # 取前三列作为经度、纬度、航向
                    input_data = data.iloc[:, :3].values

                    # 对航向进行标准化处理（转换为0-360度范围）
                    input_data[:, 2] = input_data[:, 2] % 360

                    self.orig_input.append(input_data)

            if self.orig_input:
                print(f"数据读取并切分完成，共有 {len(self.orig_input)} 组数据")
                return True
            else:
                print("数据切分后为空，请检查输入文件和切分参数。")
                return False
        except Exception as e:
            print(f"数据读取错误: {e}")
            return False

    def perform_clustering(self):
        """执行聚类分析，根据配置选择聚类方法"""
        if not self.orig_input:
            print("数据未读取，无法聚类")
            return False

        method = self.config['cluster_method']
        print(f"使用 {method} 方法进行三维聚类（经纬度+航向）...")

        # 初始化存储聚类结果的列表
        self.labels = []
        self.up_data = []
        self.down_data = []

        for input_data in self.orig_input:
            if method == 'dbscan':
                labels = self._perform_dbscan_clustering(input_data)
            elif method == 'kmeans':
                labels = self._perform_kmeans_clustering(input_data)
            elif method == 'spectral':
                labels = self._perform_spectral_clustering(input_data)
            elif method == 'mix2':
                labels = self._perform_mix2_clustering(input_data)
            elif method == 'mix3':
                labels = self._perform_mix3_clustering(input_data)
            else:
                print(f"未知聚类方法: {method}")
                return False

            # 划分上游(1)和下游(0)
            self.up_data.append(input_data[labels == 1])
            self.down_data.append(input_data[labels == 0])
            self.labels.append(labels)

        print(f"聚类完成: 上游 {sum(len(up) for up in self.up_data)} 点, 下游 {sum(len(down) for down in self.down_data)} 点")
        return True

    def adjust_labels(self, block_index, new_labels):
        """调整指定数据块的聚类标签"""
        if self.labels is not None and 0 <= block_index < len(self.labels):
            self.labels[block_index] = new_labels
            print(f"数据块 {block_index} 的标签已更新。")
        else:
            print(f"无效的数据块索引: {block_index}")

    def _fallback_clustering(self, block):
        """回退聚类方法：按经度中值分二类"""
        order = np.argsort(block[:, 0])
        half = len(order) // 2
        labels_block = np.zeros(len(block), dtype=int)
        labels_block[order[half:]] = 1
        return labels_block

    def _normalize_3d_data(self, data):
        """对三维数据进行标准化，确保各维度的贡献度平衡"""
        # 复制数据避免修改原始数据
        normalized_data = data.copy()

        # 对经纬度使用标准化
        for i in range(2):
            col_std = np.std(normalized_data[:, i])
            if col_std > 0:
                normalized_data[:, i] = (normalized_data[:, i] - np.mean(normalized_data[:, i])) / col_std

        # 对航向进行特殊处理（考虑其周期性）
        # 将航向转换为单位圆上的坐标
        heading_rad = np.radians(normalized_data[:, 2])
        heading_x = np.cos(heading_rad)
        heading_y = np.sin(heading_rad)

        # 用航向的x,y坐标替换原航向，形成4维数据
        extended_data = np.column_stack([
            normalized_data[:, 0],  # 标准化经度
            normalized_data[:, 1],  # 标准化纬度
            heading_x,  # 航向x分量
            heading_y  # 航向y分量
        ])

        return extended_data

    def compute_centroids_kmeans(self):
        """使用KMeans聚类方法计算中心点（改进版）"""
        if not self.up_data or not self.down_data:
            print("请先执行聚类")
            return False

        self.up_centers = []
        self.down_centers = []

        # 获取KMeans中心点计算的配置参数
        centroid_config = self.config.get('centroid_kmeans', {
            'n_clusters': 1,  # 每个数据块只需要一个中心点
            'max_iter': 300,
            'n_init': 10,
            'random_state': 42,
            'min_points': 5  # 最少需要的点数才进行KMeans
        })

        for block_idx, (up_block, down_block) in enumerate(zip(self.up_data, self.down_data)):
            # 处理上游数据的中心点
            if len(up_block) > 0:
                up_center = self._compute_kmeans_centroid(
                    up_block, 
                    centroid_config, 
                    f"上游数据块{block_idx}"
                )
                if up_center is not None:
                    self.up_centers.append(up_center)

            # 处理下游数据的中心点
            if len(down_block) > 0:
                down_center = self._compute_kmeans_centroid(
                    down_block, 
                    centroid_config, 
                    f"下游数据块{block_idx}"
                )
                if down_center is not None:
                    self.down_centers.append(down_center)

        print(f"KMeans中心点计算完成: 上游 {len(self.up_centers)} 点, 下游 {len(self.down_centers)} 点")
        return True

    def _compute_kmeans_centroid(self, data_block, config, block_name):
        """
        使用KMeans为单个数据块计算中心点
        
        Args:
            data_block: 数据块 (n_points, 3) - [经度, 纬度, 航向]
            config: KMeans配置参数
            block_name: 数据块名称（用于日志）
            
        Returns:
            中心点 [经度, 纬度, 航向] 或 None
        """
        if len(data_block) < config['min_points']:
            print(f"{block_name}: 点数不足({len(data_block)} < {config['min_points']})，使用几何中心")
            return self._compute_geometric_centroid(data_block)
        
        try:
            # 准备KMeans输入数据：只使用经纬度进行聚类
            # 航向由于其周期性，在中心点计算中单独处理
            geo_data = data_block[:, :2]  # 只取经纬度
            
            # 执行KMeans聚类找到最佳中心点
            kmeans = KMeans(
                n_clusters=config['n_clusters'],
                max_iter=config['max_iter'],
                n_init=config['n_init'],
                random_state=config['random_state']
            )
            
            kmeans.fit(geo_data)
            geo_centroid = kmeans.cluster_centers_[0]  # 取第一个（也是唯一的）聚类中心
            
            # 找到距离KMeans中心最近的实际数据点
            distances = np.sqrt(np.sum((geo_data - geo_centroid) ** 2, axis=1))
            closest_idx = np.argmin(distances)
            closest_point = data_block[closest_idx]
            
            # 计算该区域内航向的圆形均值
            # 定义"该区域"为距离最近点一定范围内的所有点
            distance_threshold = np.percentile(distances, 30)  # 使用30%分位数作为阈值
            nearby_mask = distances <= distance_threshold
            nearby_headings = data_block[nearby_mask, 2]
            
            # 计算航向的圆形均值
            heading_centroid = self._compute_circular_mean(nearby_headings)
            
            # 组合最终的中心点：KMeans的经纬度 + 圆形均值航向
            final_centroid = np.array([
                geo_centroid[0],  # KMeans计算的经度
                geo_centroid[1],  # KMeans计算的纬度
                heading_centroid  # 圆形均值航向
            ])
            
            print(f"{block_name}: KMeans中心点计算成功，使用了{len(nearby_headings)}个点计算航向")
            return final_centroid
            
        except Exception as e:
            print(f"{block_name}: KMeans计算失败 ({e})，使用几何中心")
            return self._compute_geometric_centroid(data_block)

    def _compute_geometric_centroid(self, data_block):
        """计算几何中心点作为备选方案"""
        centroid = np.zeros(3)
        centroid[0] = np.mean(data_block[:, 0])  # 经度均值
        centroid[1] = np.mean(data_block[:, 1])  # 纬度均值
        centroid[2] = self._compute_circular_mean(data_block[:, 2])  # 航向圆形均值
        return centroid

    def _compute_circular_mean(self, angles_deg):
        """计算角度的圆形均值"""
        angles_rad = np.radians(angles_deg)
        mean_x = np.mean(np.cos(angles_rad))
        mean_y = np.mean(np.sin(angles_rad))
        mean_angle_rad = np.arctan2(mean_y, mean_x)
        return np.degrees(mean_angle_rad) % 360

    def _perform_dbscan_clustering(self, input_data):
        """执行DBSCAN聚类（三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        cut = self.config['dbscan']['cut']
        base_eps = self.config['dbscan']['base_eps']
        min_samples = self.config['dbscan']['min_samples']
        max_eps = self.config['dbscan']['max_eps']

        iteration = int(np.ceil(len(input_data) / cut))
        labels_block = np.zeros(len(input_data), dtype=int)

        def cluster_block_with_dbscan(block):
            """
            对单个数据块使用 DBSCAN 聚类，动态调整 eps，返两簇标签或 None
            """
            # 对三维数据进行标准化
            normalized_block = self._normalize_3d_data(block)

            eps = base_eps
            while eps <= max_eps:
                preds = DBSCAN(eps=eps, min_samples=min_samples).fit_predict(normalized_block)
                valid = [lbl for lbl in set(preds) if lbl != -1]
                if len(valid) >= 2:
                    sizes = {lbl: np.sum(preds == lbl) for lbl in valid}
                    top2 = sorted(sizes, key=lambda x: sizes[x], reverse=True)[:2]
                    labels_block = np.zeros(len(preds), dtype=int)
                    labels_block[preds == top2[0]] = 1
                    labels_block[preds == top2[1]] = 0
                    return labels_block
                eps *= 0.8
            return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            labels_block = cluster_block_with_dbscan(block)
            if labels_block is None:
                # 回退：按经度中值分二类
                labels_block = self._fallback_clustering(block)
            labels_block[start:end] = labels_block # 确保标签正确应用到整个块

        return labels_block

    def _perform_kmeans_clustering(self, input_data):
        """执行KMeans聚类（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        n_clusters = self.config['kmeans']['n_clusters']
        init = self.config['kmeans']['init']
        max_iter = self.config['kmeans']['max_iter']
        n_init = self.config['kmeans']['n_init']
        random_state = self.config['kmeans']['random_state']
        cut = self.config['kmeans'].get('cut', 100)

        iteration = int(np.ceil(len(input_data) / cut))
        labels_block = np.zeros(len(input_data), dtype=int)

        def cluster_block_with_kmeans(block):
            """
            对单个数据块使用 KMeans 聚类，返回二分类标签或 None
            """
            if len(block) < n_clusters:
                return None

            try:
                # 对三维数据进行标准化
                normalized_block = self._normalize_3d_data(block)

                kmeans = KMeans(
                    n_clusters=n_clusters,
                    init=init,
                    max_iter=max_iter,
                    n_init=n_init,
                    random_state=random_state
                )
                preds = kmeans.fit_predict(normalized_block)

                # 检查是否成功分成了指定数量的簇
                unique_labels = np.unique(preds)
                if len(unique_labels) >= 2:
                    # 找到最大的两个簇
                    sizes = {lbl: np.sum(preds == lbl) for lbl in unique_labels}
                    top2 = sorted(sizes, key=lambda x: sizes[x], reverse=True)[:2]
                    labels_block = np.zeros(len(preds), dtype=int)
                    labels_block[preds == top2[0]] = 1
                    labels_block[preds == top2[1]] = 0
                    return labels_block
                else:
                    return None
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            labels_block = cluster_block_with_kmeans(block)
            if labels_block is None:
                # 回退：按经度中值分二类
                labels_block = self._fallback_clustering(block)
            labels_block[start:end] = labels_block # 确保标签正确应用到整个块

        return labels_block

    def _perform_spectral_clustering(self, input_data):
        """执行谱聚类（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        min_clusters = self.config['spectral']['min_clusters']
        max_clusters = self.config['spectral']['max_clusters']
        cut = self.config['spectral'].get('cut', 100)

        iteration = int(np.ceil(len(input_data) / cut))
        labels_block = np.zeros(len(input_data), dtype=int)

        def cluster_block_with_spectral(block):
            """
            对单个数据块使用谱聚类，返回二分类标签或 None
            """
            if len(block) < max(min_clusters, 3):  # 谱聚类至少需要3个点
                return None

            try:
                # 对三维数据进行标准化
                normalized_block = self._normalize_3d_data(block)

                spectral = SpectralClusterer(
                    min_clusters=min_clusters,
                    max_clusters=max_clusters,
                )
                preds = spectral.predict(normalized_block)

                # 检查聚类结果
                unique_labels = np.unique(preds)
                if len(unique_labels) >= 2:
                    # 找到最大的两个簇
                    sizes = {lbl: np.sum(preds == lbl) for lbl in unique_labels}
                    top2 = sorted(sizes, key=lambda x: sizes[x], reverse=True)[:2]
                    labels_block = np.zeros(len(preds), dtype=int)
                    labels_block[preds == top2[0]] = 1
                    labels_block[preds == top2[1]] = 0
                    return labels_block
                else:
                    return None
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            labels_block = cluster_block_with_spectral(block)
            if labels_block is None:
                # 回退：按经度中值分二类
                labels_block = self._fallback_clustering(block)
            labels_block[start:end] = labels_block # 确保标签正确应用到整个块

        return labels_block

    def _perform_mix2_clustering(self, input_data):
        """执行KMeans和谱聚类的混合方法（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        cut = self.config.get('mix_cut', 100)
        iteration = int(np.ceil(len(input_data) / cut))
        labels_block = np.zeros(len(input_data), dtype=int)

        # KMeans配置
        kmeans_config = self.config['kmeans']
        # 谱聚类配置
        spectral_config = self.config['spectral']

        def cluster_block_with_mix2(block):
            """
            对单个数据块使用KMeans和谱聚类混合方法
            """
            if len(block) < 3:
                return None

            try:
                # 对三维数据进行标准化
                normalized_block = self._normalize_3d_data(block)

                # 执行KMeans
                kmeans = KMeans(
                    n_clusters=kmeans_config['n_clusters'],
                    init=kmeans_config['init'],
                    max_iter=kmeans_config['max_iter'],
                    n_init=kmeans_config['n_init'],
                    random_state=kmeans_config['random_state']
                )
                kmeans_labels = kmeans.fit_predict(normalized_block)

                # 执行谱聚类
                spectral = SpectralClusterer(
                    min_clusters=spectral_config['min_clusters'],
                    max_clusters=spectral_config['max_clusters'],
                )
                spectral_labels = spectral.predict(normalized_block)

                # 合并结果：同时属于两个聚类方法的类别1为上游，其余为下游
                labels_block = np.zeros_like(kmeans_labels)
                labels_block[(kmeans_labels == 1) & (spectral_labels == 1)] = 1

                return labels_block
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            labels_block = cluster_block_with_mix2(block)
            if labels_block is None:
                # 回退：按经度中值分二类
                labels_block = self._fallback_clustering(block)
            labels_block[start:end] = labels_block # 确保标签正确应用到整个块

        return labels_block

    def _perform_mix3_clustering(self, input_data):
        """执行KMeans、谱聚类和DBSCAN的混合方法（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        cut = self.config.get('mix_cut', 100)
        iteration = int(np.ceil(len(input_data) / cut))
        labels_block = np.zeros(len(input_data), dtype=int)

        # 各聚类方法配置
        kmeans_config = self.config['kmeans']
        spectral_config = self.config['spectral']
        dbscan_config = self.config['dbscan']

        def cluster_block_with_mix3(block):
            """
            对单个数据块使用KMeans、谱聚类和DBSCAN混合方法
            """
            if len(block) < 3:
                return None

            try:
                # 对三维数据进行标准化
                normalized_block = self._normalize_3d_data(block)

                # 执行KMeans
                kmeans = KMeans(
                    n_clusters=kmeans_config['n_clusters'],
                    init=kmeans_config['init'],
                    max_iter=kmeans_config['max_iter'],
                    n_init=kmeans_config['n_init'],
                    random_state=kmeans_config['random_state']
                )
                kmeans_labels = kmeans.fit_predict(normalized_block)

                # 执行谱聚类
                spectral = SpectralClusterer(
                    min_clusters=spectral_config['min_clusters'],
                    max_clusters=spectral_config['max_clusters'],
                )
                spectral_labels = spectral.predict(normalized_block)

                # 执行DBSCAN
                dbscan_labels = DBSCAN(
                    eps=dbscan_config['base_eps'],
                    min_samples=dbscan_config['min_samples']
                ).fit_predict(normalized_block)

                # 合并结果：同时属于三个聚类方法的类别1为上游
                # 处理DBSCAN中的-1（噪声点）
                valid_dbscan = (dbscan_labels != -1)
                labels_block = np.zeros_like(kmeans_labels)
                labels_block[(kmeans_labels == 1) & (spectral_labels == 1) & (dbscan_labels == 1) & valid_dbscan] = 1

                return labels_block
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            labels_block = cluster_block_with_mix3(block)
            if labels_block is None:
                # 回退：按经度中值分二类
                labels_block = self._fallback_clustering(block)
            labels_block[start:end] = labels_block # 确保标签正确应用到整个块

        return labels_block

    def fit_splines(self):
        """拟合样条曲线（只对经纬度进行拟合，航向用于显示）"""
        if self.up_red is None or self.down_red is None:
            print("请先计算中心点")
            return False

        k = self.config['spline_degree']

        self.splines = {}
        for name, pts in [('up', self.up_red), ('down', self.down_red)]:
            if pts.shape[0] > k:
                t = np.arange(len(pts))
                # 只对经纬度进行样条拟合
                self.splines[name] = (
                    splrep(t, pts[:, 0], s=0),  # 经度样条
                    splrep(t, pts[:, 1], s=0),  # 纬度样条
                    t.min(),
                    t.max()
                )
            else:
                print(f"警告: {name} 数据点不足，无法拟合样条曲线")

        return True

    def visualize_results(self):
        """为每个数据块生成四张特定图片（修改后的风格）"""
        if not os.path.exists(self.config['save_dir']):
            os.makedirs(self.config['save_dir'])

        if self.orig_input and self.labels:
            for i, (input_data, labels) in enumerate(zip(self.orig_input, self.labels)):
                # 为每个数据块创建独立的文件夹
                block_dir = os.path.join(self.config['save_dir'], f'block_{i+1}')
                if not os.path.exists(block_dir):
                    os.makedirs(block_dir)

                # 图1: 原始数据对比图 - 灰色半透明小点
                self._plot_original_data_comparison(input_data, i, block_dir)

                # 图2: 聚类结果对比图 - 红色圆点（上游）和蓝色三角（下游）
                self._plot_clustering_results(input_data, labels, i, block_dir)

                # 图3: KMeans中心点后的聚类图 - 红色大圆与蓝色大三角
                self._plot_kmeans_centroids(i, block_dir)

                # 图4: 拟合曲线图片 - 红色与蓝色曲线并叠加中心点
                self._plot_spline_fit(i, block_dir)

                print(f"数据块 {i+1} 的图片已保存至: {block_dir}")
        else:
            print("无法生成图片，数据或标签为空。")

    def _plot_original_data_comparison(self, input_data, index, save_dir):
        """绘制原始数据对比图（灰色半透明小点）"""
        plt.figure(figsize=(12, 8))
        plt.title(f"Block {index+1}: Original Data Points")

        # 使用灰色半透明小点显示原始数据
        plt.scatter(
            input_data[:, 0],
            input_data[:, 1],
            c='gray',
            marker='.',
            s=8,
            alpha=0.5,
            label='Original Data'
        )

        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.legend()
        plt.tight_layout()

        save_path = os.path.join(save_dir, 'figure1_original_data.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"图1已保存至: {save_path}")

    def _plot_clustering_results(self, input_data, labels, index, save_dir):
        """绘制聚类结果对比图（红色圆点表示上游，蓝色三角表示下游）"""
        plt.figure(figsize=(12, 8))
        plt.title(f"Block {index+1}: Clustering Results")

        # 绘制上游数据 - 红色圆点
        plt.scatter(
            input_data[labels == 1, 0],
            input_data[labels == 1, 1],
            c='red',
            marker='o',
            s=20,
            alpha=0.7,
            label='Upstream'
        )

        # 绘制下游数据 - 蓝色三角
        plt.scatter(
            input_data[labels == 0, 0],
            input_data[labels == 0, 1],
            c='blue',
            marker='^',
            s=20,
            alpha=0.7,
            label='Downstream'
        )

        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.legend()
        plt.tight_layout()

        save_path = os.path.join(save_dir, 'figure2_clustering_results.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"图2已保存至: {save_path}")

    def _plot_kmeans_centroids(self, index, save_dir):
        """绘制KMeans中心点后的聚类图（红色大圆与蓝色大三角）"""
        plt.figure(figsize=(12, 8))
        plt.title(f"Block {index+1}: KMeans Centroids")

        # 绘制上游KMeans中心点 - 红色大圆
        if self.up_red is not None and self.up_red.size and self.up_red.ndim == 2 and self.up_red.shape[0] > 0:
            plt.scatter(
                self.up_red[:, 0],
                self.up_red[:, 1],
                c='red',
                marker='o',
                s=100,
                alpha=0.8,
                label='Upstream KMeans Centers',
                edgecolors='darkred',
                linewidth=1
            )

        # 绘制下游KMeans中心点 - 蓝色大三角
        if self.down_red is not None and self.down_red.size and self.down_red.ndim == 2 and self.down_red.shape[0] > 0:
            plt.scatter(
                self.down_red[:, 0],
                self.down_red[:, 1],
                c='blue',
                marker='^',
                s=100,
                alpha=0.8,
                label='Downstream KMeans Centers',
                edgecolors='darkblue',
                linewidth=1
            )

        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.legend()
        plt.tight_layout()

        save_path = os.path.join(save_dir, 'figure3_kmeans_centroids.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"图3已保存至: {save_path}")

    def _plot_spline_fit(self, index, save_dir):
        """绘制拟合曲线图片（红色与蓝色曲线并叠加中心点）"""
        plt.figure(figsize=(14, 10))
        plt.title(f"Block {index+1}: Spline Fit Curves")

        # 绘制上游样条拟合曲线和中心点
        if 'up' in self.splines:
            xs, ys, t0, t1 = self.splines['up']
            tplt = np.linspace(t0, t1, 200)
            plt.plot(splev(tplt, xs), splev(tplt, ys), 'r-', linewidth=3, label='Upstream Spline')

            # 叠加上游中心点
            if self.up_red is not None and self.up_red.size and self.up_red.ndim == 2 and self.up_red.shape[0] > 0:
                plt.scatter(
                    self.up_red[:, 0],
                    self.up_red[:, 1],
                    c='red',
                    marker='o',
                    s=60,
                    alpha=0.8,
                    edgecolors='darkred',
                    linewidth=1
                )

        # 绘制下游样条拟合曲线和中心点
        if 'down' in self.splines:
            xs, ys, t0, t1 = self.splines['down']
            tplt = np.linspace(t0, t1, 200)
            plt.plot(splev(tplt, xs), splev(tplt, ys), 'b-', linewidth=3, label='Downstream Spline')

            # 叠加下游中心点
            if self.down_red is not None and self.down_red.size and self.down_red.ndim == 2 and self.down_red.shape[0] > 0:
                plt.scatter(
                    self.down_red[:, 0],
                    self.down_red[:, 1],
                    c='blue',
                    marker='^',
                    s=60,
                    alpha=0.8,
                    edgecolors='darkblue',
                    linewidth=1
                )

        plt.xlabel('Longitude')
        plt.ylabel('Latitude')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.legend()
        plt.tight_layout()

        save_path = os.path.join(save_dir, 'figure4_spline_fit.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"图4已保存至: {save_path}")

    def filter_noise_points(self):
        """
        基于局部密度分析筛选噪点
        使用KD-Tree计算近邻，根据邻域内同标签点的比例判断是否为噪点
        """
        from sklearn.neighbors import KDTree
        import numpy as np

        if not self.orig_input or not self.labels:
            print("数据不完整，无法进行噪点筛选")
            return False

        # 获取配置参数
        n_neighbors = self.config['noise_filter']['n_neighbors']
        min_ratio = self.config['noise_filter']['min_ratio']
        angle_weight = self.config['noise_filter']['angle_weight']

        # 用于存储筛选后的数据
        filtered_up_data = []
        filtered_down_data = []

        # 遍历每个数据块
        for block_idx in range(len(self.orig_input)):
            input_data = self.orig_input[block_idx]
            labels = self.labels[block_idx]

            if len(input_data) < n_neighbors:
                print(f"数据块 {block_idx} 的点数少于所需邻居数，跳过噪点筛选")
                filtered_up_data.append(input_data[labels == 1])
                filtered_down_data.append(input_data[labels == 0])
                continue

            # 准备用于KD-Tree的特征数据
            # 将航向角度转换为sin和cos分量，以处理周期性
            angles_rad = np.radians(input_data[:, 2])
            features = np.column_stack([
                input_data[:, 0],  # 经度
                input_data[:, 1],  # 纬度
                np.cos(angles_rad) * angle_weight,  # 航向cos分量
                np.sin(angles_rad) * angle_weight   # 航向sin分量
            ])

            # 构建KD-Tree
            tree = KDTree(features)

            # 查找每个点的最近邻
            distances, indices = tree.query(features, k=n_neighbors)

            # 计算每个点邻域内同标签点的比例
            is_noise = np.zeros(len(input_data), dtype=bool)
            for i in range(len(input_data)):
                neighbor_labels = labels[indices[i]]
                same_label_ratio = np.sum(neighbor_labels == labels[i]) / n_neighbors
                is_noise[i] = same_label_ratio < min_ratio

            # 分离非噪点数据
            up_mask = (labels == 1) & ~is_noise
            down_mask = (labels == 0) & ~is_noise

            filtered_up = input_data[up_mask]
            filtered_down = input_data[down_mask]

            # 存储筛选后的数据
            filtered_up_data.append(filtered_up)
            filtered_down_data.append(filtered_down)

            # 打印筛选结果
            removed_up = np.sum(labels == 1) - len(filtered_up)
            removed_down = np.sum(labels == 0) - len(filtered_down)
            print(f"数据块 {block_idx}: 剔除 {removed_up} 个上游噪点, {removed_down} 个下游噪点")

        # 更新数据
        self.up_data = filtered_up_data
        self.down_data = filtered_down_data

        total_up = sum(len(up) for up in filtered_up_data)
        total_down = sum(len(down) for down in filtered_down_data)
        print(f"筛选完成: 保留上游 {total_up} 点, 下游 {total_down} 点")

        # 重新使用KMeans方法计算中心点
        self.up_centers = []
        self.down_centers = []
        self.up_red = []
        self.down_red = []

        # 获取KMeans中心点计算的配置参数
        centroid_config = self.config.get('centroid_kmeans', {
            'n_clusters': 1,
            'max_iter': 300,
            'n_init': 10,
            'random_state': 42,
            'min_points': 5
        })

        for block_idx, (up_block, down_block) in enumerate(zip(filtered_up_data, filtered_down_data)):
            # 处理上游数据的中心点
            if len(up_block) > 0:
                up_center = self._compute_kmeans_centroid(
                    up_block,
                    centroid_config,
                    f"筛选后上游数据块{block_idx}"
                )
                if up_center is not None:
                    self.up_centers.append(up_center)
                    self.up_red.append(up_center)

            # 处理下游数据的中心点
            if len(down_block) > 0:
                down_center = self._compute_kmeans_centroid(
                    down_block,
                    centroid_config,
                    f"筛选后下游数据块{block_idx}"
                )
                if down_center is not None:
                    self.down_centers.append(down_center)
                    self.down_red.append(down_center)

        # 转换为numpy数组
        self.up_red = np.array(self.up_red) if self.up_red else np.empty((0, 3))
        self.down_red = np.array(self.down_red) if self.down_red else np.empty((0, 3))

        print(f"KMeans中心点重新计算完成: 上游 {len(self.up_centers)} 点, 下游 {len(self.down_centers)} 点")

        # 生成散点图
        self.plot_filtered_scatter()

        return True

    def plot_filtered_scatter(self):
        """
        为每个数据块生成聚类和噪点筛选后的散点图
        """
        if not os.path.exists(self.config['save_dir']):
            os.makedirs(self.config['save_dir'])

        for block_idx, (up_block, down_block) in enumerate(zip(self.up_data, self.down_data)):
            # 为每个数据块创建独立的文件夹
            block_dir = os.path.join(self.config['save_dir'], f'block_{block_idx + 1}')
            if not os.path.exists(block_dir):
                os.makedirs(block_dir)

            plt.figure(figsize=(12, 8))
            plt.title(f"数据块 {block_idx + 1} KMeans中心点聚类结果")

            # 绘制上游数据点（红色）
            if isinstance(up_block, np.ndarray) and up_block.size > 0:
                plt.scatter(
                    up_block[:, 0],
                    up_block[:, 1],
                    c='red',
                    marker='o',
                    s=20,
                    alpha=0.7,
                    label=f'上游 ({len(up_block)} 点)'
                )

            # 绘制下游数据点（蓝色）
            if isinstance(down_block, np.ndarray) and down_block.size > 0:
                plt.scatter(
                    down_block[:, 0],
                    down_block[:, 1],
                    c='blue',
                    marker='^',
                    s=20,
                    alpha=0.7,
                    label=f'下游 ({len(down_block)} 点)'
                )

            # 绘制KMeans中心点
            if block_idx < len(self.up_centers):
                up_center = self.up_centers[block_idx]
                plt.scatter(
                    up_center[0],
                    up_center[1],
                    c='darkred',
                    marker='*',
                    s=200,
                    alpha=1,
                    label='上游KMeans中心点'
                )

            if block_idx < len(self.down_centers):
                down_center = self.down_centers[block_idx]
                plt.scatter(
                    down_center[0],
                    down_center[1],
                    c='darkblue',
                    marker='*',
                    s=200,
                    alpha=1,
                    label='下游KMeans中心点'
                )

            plt.xlabel('经度')
            plt.ylabel('纬度')
            plt.grid(True, linestyle='--', alpha=0.3)
            plt.legend()
            plt.tight_layout()

            save_path = os.path.join(block_dir, 'kmeans_filtered_scatter.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"数据块 {block_idx + 1} 的KMeans散点图已保存至: {save_path}")

            # 在每个数据块文件夹中也保存一份总体散点图
            if block_idx == len(self.up_data) - 1:  # 在处理最后一个数据块时生成总体图
                self._plot_all_filtered_data(block_dir)

    def _plot_all_filtered_data(self, save_dir):
        """
        生成所有数据块合并后的散点图
        """
        plt.figure(figsize=(16, 10))
        plt.title("所有数据块KMeans中心点聚类结果")

        # 合并所有上游数据点
        total_up_points = 0
        for up_block in self.up_data:
            if isinstance(up_block, np.ndarray) and up_block.size > 0:
                plt.scatter(
                    up_block[:, 0],
                    up_block[:, 1],
                    c='red',
                    marker='o',
                    s=20,
                    alpha=0.5,
                    label='上游数据点' if total_up_points == 0 else ""
                )
                total_up_points += len(up_block)

        # 合并所有下游数据点
        total_down_points = 0
        for down_block in self.down_data:
            if isinstance(down_block, np.ndarray) and down_block.size > 0:
                plt.scatter(
                    down_block[:, 0],
                    down_block[:, 1],
                    c='blue',
                    marker='^',
                    s=20,
                    alpha=0.5,
                    label='下游数据点' if total_down_points == 0 else ""
                )
                total_down_points += len(down_block)

        # 绘制所有上游KMeans中心点
        if self.up_centers:
            up_centers = np.array(self.up_centers)
            plt.scatter(
                up_centers[:, 0],
                up_centers[:, 1],
                c='darkred',
                marker='*',
                s=200,
                alpha=1,
                label='上游KMeans中心点'
            )

        # 绘制所有下游KMeans中心点
        if self.down_centers:
            down_centers = np.array(self.down_centers)
            plt.scatter(
                down_centers[:, 0],
                down_centers[:, 1],
                c='darkblue',
                marker='*',
                s=200,
                alpha=1,
                label='下游KMeans中心点'
            )

        plt.xlabel('经度')
        plt.ylabel('纬度')
        plt.grid(True, linestyle='--', alpha=0.3)

        # 添加数据点统计信息到图例
        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], marker='o', color='w', markerfacecolor='red',
                  label=f'上游数据点 ({total_up_points})', markersize=8),
            Line2D([0], [0], marker='^', color='w', markerfacecolor='blue',
                  label=f'下游数据点 ({total_down_points})', markersize=8),
            Line2D([0], [0], marker='*', color='w', markerfacecolor='darkred',
                  label=f'上游KMeans中心点 ({len(self.up_centers)})', markersize=12),
            Line2D([0], [0], marker='*', color='w', markerfacecolor='darkblue',
                  label=f'下游KMeans中心点 ({len(self.down_centers)})', markersize=12)
        ]
        plt.legend(handles=legend_elements)

        plt.tight_layout()

        save_path = os.path.join(save_dir, 'all_blocks_kmeans_scatter.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"所有数据的KMeans散点图已保存至: {save_path}")

    def save_labeled_data(self):
        """保存带标签的数据集到CSV文件"""
        if not self.up_data or not self.down_data:
            print("数据不完整，无法保存")
            return False

        # 创建保存目录
        if not os.path.exists(self.config['save_dir']):
            os.makedirs(self.config['save_dir'])

        # 用于存储所有数据
        all_data_list = []

        # 为每个数据块生成CSV文件
        for block_idx, (up_block, down_block) in enumerate(zip(self.up_data, self.down_data)):
            block_dir = os.path.join(self.config['save_dir'], f'block_{block_idx + 1}')
            if not os.path.exists(block_dir):
                os.makedirs(block_dir)

            # 合并当前数据块的上游和下游数据
            block_data = []

            # 添加上游数据（标签为1）
            if isinstance(up_block, np.ndarray) and up_block.size > 0:
                up_with_label = np.column_stack([up_block, np.ones(len(up_block))])
                block_data.append(up_with_label)

            # 添加下游数据（标签为0）
            if isinstance(down_block, np.ndarray) and down_block.size > 0:
                down_with_label = np.column_stack([down_block, np.zeros(len(down_block))])
                block_data.append(down_with_label)

            if block_data:
                # 合并当前数据块的所有数据
                block_data = np.vstack(block_data)

                # 创建DataFrame
                df = pd.DataFrame(block_data, columns=['经度', '纬度', '航向', '标签'])

                # 保存当前数据块的CSV文件
                csv_path = os.path.join(block_dir, 'kmeans_labeled_data.csv')
                df.to_csv(csv_path, index=False, encoding='utf-8')
                print(f"数据块 {block_idx + 1} 的KMeans标签数据已保存至: {csv_path}")

                # 添加到总数据列表
                all_data_list.append(block_data)

        # 合并所有数据块并保存总CSV文件
        if all_data_list:
            all_data = np.vstack(all_data_list)
            all_df = pd.DataFrame(all_data, columns=['经度', '纬度', '航向', '标签'])
            all_csv_path = os.path.join(self.config['save_dir'], 'all_kmeans_labeled_data.csv')
            all_df.to_csv(all_csv_path, index=False, encoding='utf-8')
            print(f"所有数据的KMeans标签数据已保存至: {all_csv_path}")

        return True

    def run_full_pipeline(self):
        """运行完整处理管道（使用KMeans中心点方法）"""
        print("开始运行三维数据处理与聚类分析管道（经纬度+航向）- KMeans中心点版本...")

        if not self.read_and_partition_data():
            print("数据读取失败，终止处理")
            return False

        if not self.perform_clustering():
            print("聚类分析失败，终止处理")
            return False

        if not self.compute_centroids_kmeans():
            print("KMeans中心点计算失败，终止处理")
            return False

        # 设置up_red和down_red为KMeans计算的中心点
        self.up_red = np.array(self.up_centers) if self.up_centers else np.empty((0, 3))
        self.down_red = np.array(self.down_centers) if self.down_centers else np.empty((0, 3))

        if not self.filter_noise_points():
            print("噪点筛选失败，终止处理")
            return False

        # 不需要再次调用compute_centroids_kmeans，因为filter_noise_points已经重新计算了中心点
        if not self.fit_splines():
            print("样条拟合失败，终止处理")
            return False

        self.visualize_results()

        # 添加保存标签数据的步骤
        if not self.save_labeled_data():
            print("标签数据保存失败，终止处理")
            return False

        print(f"KMeans中心点处理完成！所有结果已保存至: {self.config['save_dir']}")
        return True


# 配置参数集中定义
cluster_method = 'dbscan'  # 可选: 'dbscan', 'kmeans', 'spectral', 'mix2', 'mix3'
save_dir = os.path.join(r'E:\实验代码备份\project 1 trajectory cluster\3\pictures', f'{cluster_method}_kmeans_centroids')

config = {
    'file_path': r"E:\实验代码备份\ais data\excel_output_长江\all\整合数据_经纬度航向.csv",
    'save_dir': save_dir,
    'cluster_method': cluster_method,
    'partition_count': 20,  # 切分数量

    # KMeans中心点计算参数（新增）
    'centroid_kmeans': {
        'n_clusters': 1,        # 每个数据块只需要一个中心点
        'max_iter': 300,        # 最大迭代次数
        'n_init': 10,           # 不同初始化的运行次数
        'random_state': 42,     # 随机种子，确保结果可重现
        'min_points': 5         # 最少需要的点数才进行KMeans，否则使用几何中心
    },

    # 噪点筛选参数
    'noise_filter': {
        'n_neighbors': 50,  # 计算局部密度时考虑的邻居数量
        'min_ratio': 0.5,   # 邻域内同标签点的最小比例
        'angle_weight': 0.3 # 航向在距离计算中的权重
    },

    # DBSCAN参数（针对三维数据调整）
    'dbscan': {
        'cut': 10000,
        'base_eps': 0.3,
        'min_samples': 5,
        'max_eps': 1.5
    },

    # KMeans参数
    'kmeans': {
        'n_clusters': 2,
        'init': 'k-means++',
        'max_iter': 300,
        'n_init': 10,
        'random_state': 0,
        'cut': 100
    },

    # 谱聚类参数
    'spectral': {
        'min_clusters': 2,
        'max_clusters': 2,
        'cut': 100
    },

    # 混合方法的分块大小
    'mix_cut': 100,

    # 样条拟合参数
    'spline_degree': 3,

    # 新增参数
    'target_sheet': 'Sheet1'  # 假设目标sheet名为'Sheet1'
}

# 主程序
if __name__ == "__main__":
    # 创建处理器实例
    processor = GeoDataProcessor(config)

    # 运行完整管道
    if processor.run_full_pipeline():
        print("KMeans中心点版本程序成功完成！")
    else:
        print("程序执行过程中出现错误！")
