import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.neighbors import KDTree
import os

class DensityAnalyzer:
    """基于KD-Tree的局部密度分析器"""

    def __init__(self, config):
        """初始化分析器，使用配置字典设置参数"""
        self.config = config
        self.data = None
        self.labels = None
        self.up_data = None
        self.down_data = None
        self.up_centers = None
        self.down_centers = None

    def load_data(self, data_path):
        """加载数据文件（经度、纬度、航向）"""
        try:
            # 读取CSV文件
            df = pd.read_csv(data_path)
            if df.shape[1] >= 4:  # 假设格式为：经度、纬度、航向、标签
                self.data = df.iloc[:, :3].values  # 取前三列作为特征
                self.labels = df.iloc[:, 3].values  # 第四列作为标签
                
                # 对航向进行标准化处理（转换为0-360度范围）
                self.data[:, 2] = self.data[:, 2] % 360

                # 初始分离上下游数据
                self.up_data = self.data[self.labels == 1]
                self.down_data = self.data[self.labels == 0]

                print(f"数据加载完成：共 {len(self.data)} 个点")
                print(f"上游点数：{len(self.up_data)}，下游点数：{len(self.down_data)}")
                return True
            else:
                print("数据格式不正确，需要至少4列：经度、纬度、航向、标签")
                return False
        except Exception as e:
            print(f"数据读取错误: {e}")
            return False

    def filter_noise_points(self):
        """
        基于局部密度分析筛选噪点
        使用KD-Tree计算近邻，根据邻域内同标签点的比例判断是否为噪点
        """
        from sklearn.neighbors import KDTree
        import numpy as np

        if not self.orig_input or not self.labels:
            print("数据不完整，无法进行噪点筛选")
            return False

        # 获取配置参数
        n_neighbors = self.config['noise_filter']['n_neighbors']
        min_ratio = self.config['noise_filter']['min_ratio']
        angle_weight = self.config['noise_filter']['angle_weight']

        # 用于存储筛选后的数据
        filtered_up_data = []
        filtered_down_data = []
        
        # 用于存储所有数据块的数据
        all_filtered_data = []

        # 遍历每个数据块
        for block_idx in range(len(self.orig_input)):
            input_data = self.orig_input[block_idx]
            labels = self.labels[block_idx]

            if len(input_data) < n_neighbors:
                print(f"数据块 {block_idx} 的点数少于所需邻居数，跳过噪点筛选")
                filtered_up_data.append(input_data[labels == 1])
                filtered_down_data.append(input_data[labels == 0])
                continue

            # 准备用于KD-Tree的特征数据
            angles_rad = np.radians(input_data[:, 2])
            features = np.column_stack([
                input_data[:, 0],  # 经度
                input_data[:, 1],  # 纬度
                np.cos(angles_rad) * angle_weight,  # 航向cos分量
                np.sin(angles_rad) * angle_weight   # 航向sin分量
            ])

            # 构建KD-Tree
            tree = KDTree(features)

            # 查找每个点的最近邻
            distances, indices = tree.query(features, k=n_neighbors)

            # 计算每个点邻域内同标签点的比例
            is_noise = np.zeros(len(input_data), dtype=bool)
            for i in range(len(input_data)):
                neighbor_labels = labels[indices[i]]
                same_label_ratio = np.sum(neighbor_labels == labels[i]) / n_neighbors
                is_noise[i] = same_label_ratio < min_ratio

            # 分离非噪点数据
            up_mask = (labels == 1) & ~is_noise
            down_mask = (labels == 0) & ~is_noise

            filtered_up = input_data[up_mask]
            filtered_down = input_data[down_mask]

            # 存储筛选后的数据
            filtered_up_data.append(filtered_up)
            filtered_down_data.append(filtered_down)

            # 创建数据块文件夹
            block_dir = os.path.join(self.config['save_dir'], f'block_{block_idx + 1}')
            if not os.path.exists(block_dir):
                os.makedirs(block_dir)

            # 准备当前数据块的所有数据（包括标签）
            block_data = pd.DataFrame(np.vstack([filtered_up, filtered_down]), 
                                    columns=['经度', '纬度', '航向'])
            block_data['标签'] = np.concatenate([
                np.ones(len(filtered_up), dtype=int),
                np.zeros(len(filtered_down), dtype=int)
            ])
            block_data['数据块'] = block_idx + 1

            # 保存当前数据块的数据
            block_save_path = os.path.join(block_dir, 'filtered_data.csv')
            block_data.to_csv(block_save_path, index=False, encoding='utf-8')
            print(f"数据块 {block_idx + 1} 的数据已保存至: {block_save_path}")

            # 添加到全部数据列表
            all_filtered_data.append(block_data)

            # 打印筛选结果
            removed_up = np.sum(labels == 1) - len(filtered_up)
            removed_down = np.sum(labels == 0) - len(filtered_down)
            print(f"数据块 {block_idx}: 剔除 {removed_up} 个上游噪点, {removed_down} 个下游噪点")

        # 更新数据
        self.up_data = filtered_up_data
        self.down_data = filtered_down_data

        # 保存所有数据块的合并数据
        all_data = pd.concat(all_filtered_data, ignore_index=True)
        all_data_save_path = os.path.join(self.config['save_dir'], 'all_filtered_data.csv')
        all_data.to_csv(all_data_save_path, index=False, encoding='utf-8')
        print(f"所有数据块的合并数据已保存至: {all_data_save_path}")

        total_up = sum(len(up) for up in filtered_up_data)
        total_down = sum(len(down) for down in filtered_down_data)
        print(f"筛选完成: 保留上游 {total_up} 点, 下游 {total_down} 点")

        # 重新计算中心点
        self.up_centers = []
        self.down_centers = []
        self.up_red = []
        self.down_red = []

        for up_block, down_block in zip(filtered_up_data, filtered_down_data):
            # 计算上游数据的中心点
            if len(up_block) > 0:
                up_sorted = up_block[np.argsort(up_block[:, 0])]  # 按经度排序
                up_center = up_sorted[len(up_sorted) // 2]  # 选取中间值
                self.up_centers.append(up_center)
                self.up_red.append(up_center)

            # 计算下游数据的中心点
            if len(down_block) > 0:
                down_sorted = down_block[np.argsort(down_block[:, 0])]  # 按经度排序
                down_center = down_sorted[len(down_sorted) // 2]  # 选取中间值
                self.down_centers.append(down_center)
                self.down_red.append(down_center)

        # 转换为numpy数组
        self.up_red = np.array(self.up_red) if self.up_red else np.empty((0, 3))
        self.down_red = np.array(self.down_red) if self.down_red else np.empty((0, 3))

        print(f"中心点重新计算完成: 上游 {len(self.up_centers)} 点, 下游 {len(self.down_centers)} 点")
        
        # 生成散点图
        self.plot_filtered_scatter()
        
        return True

    def _compute_centers(self):
        """计算上下游数据的中心点"""
        if len(self.up_data) > 0:
            up_sorted = self.up_data[np.argsort(self.up_data[:, 0])]
            self.up_centers = up_sorted[len(up_sorted) // 2]

        if len(self.down_data) > 0:
            down_sorted = self.down_data[np.argsort(self.down_data[:, 0])]
            self.down_centers = down_sorted[len(down_sorted) // 2]

    def plot_results(self):
        """绘制分析结果"""
        if not os.path.exists(self.config['output']['save_dir']):
            os.makedirs(self.config['output']['save_dir'])

        plt.figure(figsize=(16, 10))
        plt.title("局部密度分析结果")

        # 绘制上游数据点（红色）
        if len(self.up_data) > 0:
            plt.scatter(
                self.up_data[:, 0],
                self.up_data[:, 1],
                c='red',
                marker='o',
                s=20,
                alpha=0.7,
                label=f'上游 ({len(self.up_data)} 点)'
            )

        # 绘制下游数据点（蓝色）
        if len(self.down_data) > 0:
            plt.scatter(
                self.down_data[:, 0],
                self.down_data[:, 1],
                c='blue',
                marker='^',
                s=20,
                alpha=0.7,
                label=f'下游 ({len(self.down_data)} 点)'
            )

        # 绘制中心点
        if self.up_centers is not None:
            plt.scatter(
                self.up_centers[0],
                self.up_centers[1],
                c='darkred',
                marker='*',
                s=200,
                alpha=1,
                label='上游中心点'
            )

        if self.down_centers is not None:
            plt.scatter(
                self.down_centers[0],
                self.down_centers[1],
                c='darkblue',
                marker='*',
                s=200,
                alpha=1,
                label='下游中心点'
            )

        plt.xlabel('经度')
        plt.ylabel('纬度')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.legend()
        plt.tight_layout()

        save_path = os.path.join(
            self.config['output']['save_dir'], 
            self.config['output']['plot_name']
        )
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"分析结果图片已保存至: {save_path}")

    def save_results(self):
        """保存处理后的数据"""
        if self.up_data is None or self.down_data is None:
            print("请先进行密度分析")
            return False

        # 准备保存的数据
        up_df = pd.DataFrame(self.up_data, columns=['经度', '纬度', '航向'])
        up_df['标签'] = 1
        down_df = pd.DataFrame(self.down_data, columns=['经度', '纬度', '航向'])
        down_df['标签'] = 0

        # 合并数据
        result_df = pd.concat([up_df, down_df], ignore_index=True)

        # 保存到文件
        save_path = os.path.join(
            self.config['output']['save_dir'], 
            self.config['output']['data_name']
        )
        result_df.to_csv(save_path, index=False)
        print(f"处理后的数据已保存至: {save_path}")
        return True

def main():
    # 配置参数
    config = {
        # 输入输出路径配置
        'input': {
            'data_file': r'E:\实验代码备份\project 1 trajectory cluster\clustered_data.csv'  # 输入数据文件路径
        },
        'output': {
            'save_dir': r'E:\实验代码备份\project 1 trajectory cluster\density_analysis_results',  # 结果保存目录
            'plot_name': 'density_analysis.png',  # 图片文件名
            'data_name': 'filtered_data.csv'      # 处理后数据文件名
        },
        # 噪点筛选参数
        'noise_filter': {
            'n_neighbors': 50,     # 计算局部密度时考虑的邻居数量
            'min_ratio': 0.5,      # 邻域内同标签点的最小比例
            'angle_weight': 0.3    # 航向在距离计算中的权重
        }
    }

    # 创建分析器实例
    analyzer = DensityAnalyzer(config)

    # 加载数据
    if not analyzer.load_data(config['input']['data_file']):
        return

    # 执行密度分析
    if not analyzer.filter_noise_points():
        return

    # 可视化结果
    analyzer.plot_results()

    # 保存处理后的数据
    analyzer.save_results()

if __name__ == "__main__":
    main() 