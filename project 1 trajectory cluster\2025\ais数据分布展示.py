#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIS数据分布展示脚本
功能：
1. 合并多个CSV文件的AIS数据
2. 生成高质量的学术论文级散点图
3. 保存汇总数据和可视化结果

作者：AI Assistant
日期：2025-01-07
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import numpy as np
from pathlib import Path

# 设置学术论文级字体和样式
plt.rcParams['font.family'] = ['Times New Roman', 'serif']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.0
plt.rcParams['axes.spines.top'] = False
plt.rcParams['axes.spines.right'] = False
plt.rcParams['xtick.direction'] = 'in'
plt.rcParams['ytick.direction'] = 'in'
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['savefig.bbox'] = 'tight'

class AISDataProcessor:
    """AIS数据处理和可视化类"""

    def __init__(self):
        """初始化数据处理器"""
        # 输入文件路径
        self.input_files = [
            r"E:\实验代码备份\ais data\excel_output_长江\切2\筛选结果_经纬度航向.csv",
            r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_经纬度航向.csv",
            r"E:\实验代码备份\ais data\excel_output_长江\切3\筛选结果_经纬度航向.csv"
        ]

        # 输出文件路径
        self.output_csv = r"E:\实验代码备份\ais data\excel_output_长江\汇总_筛选结果_经纬度航向.csv"
        self.output_png = r"E:\实验代码备份\ais data\excel_output_长江\AIS数据分布_学术版.png"
        self.output_pdf = r"E:\实验代码备份\ais data\excel_output_长江\AIS数据分布_学术版.pdf"

        # 数据存储
        self.merged_data = None
        self.valid_data = None

    def load_and_merge_data(self):
        """加载并合并多个CSV文件"""
        print("=" * 60)
        print("🔄 开始加载和合并AIS数据文件")
        print("=" * 60)

        all_dataframes = []
        total_records = 0

        for i, file_path in enumerate(self.input_files, 1):
            try:
                print(f"📂 正在读取文件 {i}/3: {os.path.basename(file_path)}")

                if not os.path.exists(file_path):
                    print(f"⚠️  文件不存在，跳过: {file_path}")
                    continue

                # 尝试多种编码格式读取
                df = None
                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        print(f"   ✅ 成功读取 ({encoding} 编码): {len(df):,} 条记录")
                        break
                    except UnicodeDecodeError:
                        continue

                if df is None:
                    print(f"   ❌ 无法读取文件: {file_path}")
                    continue

                # 添加数据源标识
                df['data_source'] = f"切{i if i != 2 else '1' if '切1' in file_path else '2' if '切2' in file_path else '3'}"
                all_dataframes.append(df)
                total_records += len(df)

            except Exception as e:
                print(f"   ❌ 读取文件失败: {e}")
                continue

        if not all_dataframes:
            raise ValueError("❌ 没有成功读取任何数据文件")

        # 合并所有数据
        print(f"\n🔗 合并 {len(all_dataframes)} 个数据文件...")
        self.merged_data = pd.concat(all_dataframes, ignore_index=True)

        print(f"✅ 数据合并完成:")
        print(f"   - 总记录数: {len(self.merged_data):,}")
        print(f"   - 数据列: {list(self.merged_data.columns)}")

        return self.merged_data

    def clean_and_validate_data(self):
        """清理和验证数据"""
        print(f"\n🧹 数据清理和验证...")

        if self.merged_data is None:
            raise ValueError("请先加载数据")

        # 检查必要的列
        required_columns = ['latitude', 'longitude']
        missing_columns = [col for col in required_columns if col not in self.merged_data.columns]

        if missing_columns:
            print(f"❌ 缺少必要的列: {missing_columns}")
            print(f"   可用列: {list(self.merged_data.columns)}")
            raise ValueError(f"数据文件缺少必要的列: {missing_columns}")

        # 数据清理 - 只移除NaN值，不做异常值检测
        original_count = len(self.merged_data)

        # 移除NaN值
        self.valid_data = self.merged_data.dropna(subset=['latitude', 'longitude']).copy()
        nan_removed = original_count - len(self.valid_data)

        print(f"   - 原始记录: {original_count:,}")
        print(f"   - 移除NaN值: {nan_removed:,}")
        print(f"   - 有效记录: {len(self.valid_data):,}")
        print(f"   - 数据保留率: {len(self.valid_data)/original_count*100:.1f}%")

        # 数据统计 - 修正：数据文件中的列名是反的
        print(f"\n📊 数据范围统计:")
        print(f"   - 经度范围: {self.valid_data['latitude'].min():.6f} ~ {self.valid_data['latitude'].max():.6f}")
        print(f"   - 纬度范围: {self.valid_data['longitude'].min():.6f} ~ {self.valid_data['longitude'].max():.6f}")

        return self.valid_data

    def save_merged_data(self):
        """保存合并后的数据到CSV文件"""
        print(f"\n💾 保存汇总数据...")

        if self.valid_data is None:
            raise ValueError("请先清理数据")

        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(self.output_csv)
            os.makedirs(output_dir, exist_ok=True)

            # 保存数据
            self.valid_data.to_csv(self.output_csv, index=False, encoding='utf-8-sig')

            print(f"✅ 汇总数据已保存:")
            print(f"   - 文件路径: {self.output_csv}")
            print(f"   - 数据行数: {len(self.valid_data):,}")
            print(f"   - 文件大小: {os.path.getsize(self.output_csv) / 1024 / 1024:.2f} MB")

            return True

        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
            return False

    def create_academic_plot(self):
        """创建学术论文级的散点图"""
        print(f"\n📈 生成学术论文级散点图...")

        if self.valid_data is None or len(self.valid_data) == 0:
            print("❌ 没有有效数据可用于绘图")
            return False

        try:
            # 提取经纬度数据 - 修正：数据文件中的列名是反的
            # 文件中的'latitude'列实际是经度，'longitude'列实际是纬度
            longitude = self.valid_data['latitude']   # 实际的经度数据
            latitude = self.valid_data['longitude']   # 实际的纬度数据

            # 检查数据是否有效
            if longitude.empty or latitude.empty:
                print("❌ 经纬度数据为空")
                return False

            print(f"   - 实际经度范围: {longitude.min():.6f} ~ {longitude.max():.6f}")
            print(f"   - 实际纬度范围: {latitude.min():.6f} ~ {latitude.max():.6f}")

            # 创建图形
            fig, ax = plt.subplots(figsize=(10, 8))

            # 绘制散点图 (x轴=经度, y轴=纬度)
            scatter = ax.scatter(longitude, latitude,
                               c='black',           # 黑色点，适合期刊发表
                               s=0.5,               # 小点尺寸
                               alpha=0.6,           # 适当透明度
                               edgecolors='none',   # 无边框
                               rasterized=True)     # 栅格化以减小文件大小

            # 设置坐标轴标签 (Times New Roman, 12pt)
            ax.set_xlabel('Longitude (°)', fontfamily='Times New Roman', fontsize=12)
            ax.set_ylabel('Latitude (°)', fontfamily='Times New Roman', fontsize=12)

            # 设置刻度标签字体
            for label in ax.get_xticklabels() + ax.get_yticklabels():
                label.set_fontfamily('Times New Roman')
                label.set_fontsize(10)

            # 移除上边框和右边框
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)

            # 设置网格
            ax.grid(True, linestyle='-', alpha=0.2, linewidth=0.5)
            ax.set_axisbelow(True)

            # 设置坐标轴范围（自动调整边距）
            lon_margin = (longitude.max() - longitude.min()) * 0.02
            lat_margin = (latitude.max() - latitude.min()) * 0.02

            ax.set_xlim(longitude.min() - lon_margin, longitude.max() + lon_margin)
            ax.set_ylim(latitude.min() - lat_margin, latitude.max() + lat_margin)

            # 设置坐标轴比例
            ax.set_aspect('equal', adjustable='box')

            # 紧凑布局
            plt.tight_layout()

            # 保存为PNG格式（高分辨率）
            print(f"   💾 保存PNG格式...")
            plt.savefig(self.output_png,
                       dpi=300,
                       bbox_inches='tight',
                       facecolor='white',
                       edgecolor='none',
                       format='png')

            # 保存为PDF格式（矢量图）
            print(f"   💾 保存PDF格式...")
            plt.savefig(self.output_pdf,
                       bbox_inches='tight',
                       facecolor='white',
                       edgecolor='none',
                       format='pdf')

            print(f"✅ 图表保存完成:")
            print(f"   - PNG文件: {self.output_png}")
            print(f"   - PDF文件: {self.output_pdf}")
            print(f"   - 数据点数: {len(self.valid_data):,}")
            print(f"   - 分辨率: 300 DPI")

            # 显示图表
            plt.show()

            return True

        except Exception as e:
            print(f"❌ 生成图表失败: {e}")
            return False
        finally:
            plt.close()

    def generate_summary_report(self):
        """生成数据处理摘要报告"""
        print(f"\n📋 数据处理摘要报告")
        print("=" * 60)

        if self.valid_data is None:
            print("❌ 没有可用数据")
            return

        # 按数据源统计
        if 'data_source' in self.valid_data.columns:
            print("📊 各数据源统计:")
            source_stats = self.valid_data['data_source'].value_counts()
            for source, count in source_stats.items():
                print(f"   - {source}: {count:,} 条记录 ({count/len(self.valid_data)*100:.1f}%)")

        # 数据质量统计
        print(f"\n📈 数据质量指标:")
        print(f"   - 总记录数: {len(self.valid_data):,}")
        print(f"   - 经度范围: {self.valid_data['longitude'].min():.6f} ~ {self.valid_data['longitude'].max():.6f}")
        print(f"   - 纬度范围: {self.valid_data['latitude'].min():.6f} ~ {self.valid_data['latitude'].max():.6f}")
        print(f"   - 覆盖区域: {(self.valid_data['longitude'].max() - self.valid_data['longitude'].min()):.6f}° × {(self.valid_data['latitude'].max() - self.valid_data['latitude'].min()):.6f}°")

        # 输出文件信息
        print(f"\n📁 输出文件:")
        print(f"   - 汇总CSV: {self.output_csv}")
        print(f"   - 散点图PNG: {self.output_png}")
        print(f"   - 散点图PDF: {self.output_pdf}")

        print("=" * 60)


def main():
    """主函数"""
    print("🚀 AIS数据分布展示程序")
    print("功能：合并多个CSV文件并生成学术论文级散点图")
    print("=" * 60)

    # 创建数据处理器
    processor = AISDataProcessor()

    try:
        # 1. 加载和合并数据
        merged_data = processor.load_and_merge_data()

        # 2. 清理和验证数据
        valid_data = processor.clean_and_validate_data()

        # 3. 保存汇总数据
        if processor.save_merged_data():
            print("✅ 汇总数据保存成功")
        else:
            print("⚠️  汇总数据保存失败，但继续处理")

        # 4. 生成学术论文级散点图
        if processor.create_academic_plot():
            print("✅ 学术论文级散点图生成成功")
        else:
            print("❌ 散点图生成失败")
            return False

        # 5. 生成摘要报告
        processor.generate_summary_report()

        print("\n🎉 所有处理完成！")
        print("📝 输出文件说明:")
        print("   - CSV文件：可用于进一步数据分析")
        print("   - PNG文件：适合网页展示和预览")
        print("   - PDF文件：适合学术论文插图（矢量格式）")

        return True

    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")
        print("💡 请检查输入文件路径是否正确")
        return False

    except ValueError as e:
        print(f"❌ 数据错误: {e}")
        print("💡 请检查数据文件格式和内容")
        return False

    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()

    if success:
        print("\n✅ 程序执行成功！")
    else:
        print("\n❌ 程序执行失败！")

    # 等待用户输入（可选）
    input("\n按回车键退出...")