import csv
from openpyxl import Workbook
from openpyxl.styles import Font
import os


def log_to_excel():
    """将日志文件分批转换为多个Excel文件"""
    # 修改为实际的输入日志文件路径
    log_file = r"E:\实验代码备份\ais data\长江\ais-data-2024-11-02-1.log\ais-data-2024-11-02-1.log"

    # 输出目录，会在该目录下生成多个Excel文件
    output_dir = r"E:\实验代码备份\ais data\excel_output"

    # 每个Excel文件的最大行数（Excel限制为1048576，留出余量设为1000000）
    max_rows_per_file = 1000000

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 定义Excel表头
    headers = [
        'mmsi', '经度', '纬度', '更新时间（毫秒数）', '速度（节）',
        '状态', '艏向（°）', '航向（°）', '船舶类型', '接收站类型',
        '吃水（米）', '宽度（米）', '长度（米）', '英文船名', '中文船名'
    ]

    try:
        # 读取所有行
        with open(log_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=',')
            all_rows = list(reader)

        total_rows = len(all_rows)
        print(f"日志文件共有 {total_rows} 条记录")

        # 计算需要生成的文件数量
        file_count = (total_rows // max_rows_per_file) + (1 if total_rows % max_rows_per_file > 0 else 0)

        # 分批处理数据
        for i in range(file_count):
            start_idx = i * max_rows_per_file
            end_idx = min((i + 1) * max_rows_per_file, total_rows)

            # 创建新的工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "船舶数据"

            # 写入表头
            ws.append(headers)
            for cell in ws[1]:
                cell.font = Font(bold=True)

            # 写入当前批次的数据
            for row in all_rows[start_idx:end_idx]:
                if not row or all(not cell.strip() for cell in row):
                    continue
                ws.append(row)

            # 自动调整列宽
            for column_cells in ws.columns:
                length = max(len(str(cell.value)) for cell in column_cells)
                ws.column_dimensions[column_cells[0].column_letter].width = length * 1.2

            # 生成输出文件名（例如：ais-data-2024-07-01-1_part1.xlsx）
            base_name = os.path.basename(log_file)
            base_name_no_ext = os.path.splitext(base_name)[0]
            output_file = os.path.join(output_dir, f"{base_name_no_ext}_part{i + 1}.xlsx")

            # 保存文件
            wb.save(output_file)
            print(f"已生成文件 {output_file}，包含 {end_idx - start_idx} 条记录")

        print(f"共处理 {total_rows} 条记录，生成 {file_count} 个Excel文件")

    except Exception as e:
        print(f"转换失败: {str(e)}")


if __name__ == "__main__":
    log_to_excel()