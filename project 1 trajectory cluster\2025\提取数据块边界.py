"""
提取指定数据块的边界数据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入数据切分模块
from 数据切分模块 import partition_data as dp_partition_data

# 设置中文字体支持
plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]
plt.rcParams["axes.unicode_minus"] = False


def get_block_boundaries(file_path, partition_count, target_block_index):
    """
    获取指定数据块的边界信息
    
    Args:
        file_path: 数据文件路径
        partition_count: 切分数量
        target_block_index: 目标数据块索引（从0开始）
        
    Returns:
        dict: 包含边界信息的字典
    """
    try:
        # 使用数据切分模块读取和切分数据
        data_list = dp_partition_data(file_path, partition_count)
        
        if not data_list or target_block_index >= len(data_list):
            print(f"❌ 数据块索引 {target_block_index} 超出范围（共有 {len(data_list)} 个数据块）")
            return None
        
        # 获取目标数据块
        target_block = data_list[target_block_index]
        
        if target_block is None or target_block.empty:
            print(f"❌ 数据块 {target_block_index} 为空")
            return None
        
        # 计算边界信息
        boundaries = {
            'block_index': target_block_index,
            'total_points': len(target_block),
            'longitude_min': target_block.iloc[:, 0].min(),
            'longitude_max': target_block.iloc[:, 0].max(),
            'latitude_min': target_block.iloc[:, 1].min(),
            'latitude_max': target_block.iloc[:, 1].max(),
            'longitude_range': target_block.iloc[:, 0].max() - target_block.iloc[:, 0].min(),
            'latitude_range': target_block.iloc[:, 1].max() - target_block.iloc[:, 1].min(),
        }
        
        # 如果有航向数据
        if target_block.shape[1] >= 3:
            boundaries['heading_min'] = target_block.iloc[:, 2].min()
            boundaries['heading_max'] = target_block.iloc[:, 2].max()
            boundaries['heading_range'] = target_block.iloc[:, 2].max() - target_block.iloc[:, 2].min()
        
        # 计算边界点（四个角点）
        boundaries['corner_points'] = {
            'top_left': (boundaries['longitude_min'], boundaries['latitude_max']),
            'top_right': (boundaries['longitude_max'], boundaries['latitude_max']),
            'bottom_left': (boundaries['longitude_min'], boundaries['latitude_min']),
            'bottom_right': (boundaries['longitude_max'], boundaries['latitude_min'])
        }
        
        # 计算中心点
        boundaries['center_point'] = (
            (boundaries['longitude_min'] + boundaries['longitude_max']) / 2,
            (boundaries['latitude_min'] + boundaries['latitude_max']) / 2
        )
        
        return boundaries, target_block
        
    except Exception as e:
        print(f"❌ 获取边界信息时发生错误: {e}")
        return None


def visualize_block_boundaries(boundaries, block_data, save_path=None):
    """
    可视化数据块边界
    
    Args:
        boundaries: 边界信息字典
        block_data: 数据块DataFrame
        save_path: 保存路径（可选）
    """
    try:
        plt.figure(figsize=(12, 8))
        
        # 绘制数据点
        lons = block_data.iloc[:, 0]
        lats = block_data.iloc[:, 1]
        plt.scatter(lons, lats, c='blue', s=10, alpha=0.6, label='数据点')
        
        # 绘制边界矩形
        corners = boundaries['corner_points']
        rect_lons = [corners['bottom_left'][0], corners['bottom_right'][0], 
                    corners['top_right'][0], corners['top_left'][0], corners['bottom_left'][0]]
        rect_lats = [corners['bottom_left'][1], corners['bottom_right'][1], 
                    corners['top_right'][1], corners['top_left'][1], corners['bottom_left'][1]]
        plt.plot(rect_lons, rect_lats, 'r-', linewidth=2, label='边界')
        
        # 标记中心点
        center = boundaries['center_point']
        plt.scatter(center[0], center[1], c='red', s=100, marker='x', linewidth=3, label='中心点')
        
        # 标记四个角点
        for corner_name, (lon, lat) in corners.items():
            plt.scatter(lon, lat, c='green', s=80, marker='s', label=corner_name if corner_name == 'top_left' else "")
        
        plt.title(f'数据块 {boundaries["block_index"] + 1} 边界信息\n'
                 f'数据点数: {boundaries["total_points"]}', fontsize=14, fontweight='bold')
        plt.xlabel('经度 (Longitude)', fontsize=12)
        plt.ylabel('纬度 (Latitude)', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 添加边界信息文本
        info_text = f'经度范围: {boundaries["longitude_min"]:.6f} ~ {boundaries["longitude_max"]:.6f}\n'
        info_text += f'纬度范围: {boundaries["latitude_min"]:.6f} ~ {boundaries["latitude_max"]:.6f}\n'
        info_text += f'经度跨度: {boundaries["longitude_range"]:.6f}\n'
        info_text += f'纬度跨度: {boundaries["latitude_range"]:.6f}'
        
        plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 边界可视化图已保存: {save_path}")
        
        plt.show()
        
    except Exception as e:
        print(f"❌ 可视化过程中发生错误: {e}")


def save_boundary_data(boundaries, block_data, output_dir):
    """
    保存边界数据到文件
    
    Args:
        boundaries: 边界信息字典
        block_data: 数据块DataFrame
        output_dir: 输出目录
    """
    try:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        block_index = boundaries['block_index']
        
        # 保存数据块数据
        data_file = output_path / f"数据块_{block_index + 1}_数据.csv"
        block_data.to_csv(data_file, index=False, encoding='utf-8-sig')
        
        # 保存边界信息
        boundary_file = output_path / f"数据块_{block_index + 1}_边界信息.txt"
        with open(boundary_file, 'w', encoding='utf-8') as f:
            f.write(f"数据块 {block_index + 1} 边界信息\n")
            f.write("=" * 40 + "\n")
            f.write(f"数据点数量: {boundaries['total_points']}\n")
            f.write(f"经度范围: {boundaries['longitude_min']:.6f} ~ {boundaries['longitude_max']:.6f}\n")
            f.write(f"纬度范围: {boundaries['latitude_min']:.6f} ~ {boundaries['latitude_max']:.6f}\n")
            f.write(f"经度跨度: {boundaries['longitude_range']:.6f}\n")
            f.write(f"纬度跨度: {boundaries['latitude_range']:.6f}\n")
            
            if 'heading_min' in boundaries:
                f.write(f"航向范围: {boundaries['heading_min']:.2f} ~ {boundaries['heading_max']:.2f}\n")
                f.write(f"航向跨度: {boundaries['heading_range']:.2f}\n")
            
            f.write(f"中心点: ({boundaries['center_point'][0]:.6f}, {boundaries['center_point'][1]:.6f})\n")
            f.write("\n四个角点坐标:\n")
            for corner_name, (lon, lat) in boundaries['corner_points'].items():
                f.write(f"  {corner_name}: ({lon:.6f}, {lat:.6f})\n")
        
        print(f"✅ 数据已保存:")
        print(f"  - 数据文件: {data_file}")
        print(f"  - 边界信息: {boundary_file}")
        
    except Exception as e:
        print(f"❌ 保存数据时发生错误: {e}")


def main():
    """主函数"""
    # 配置参数
    file_path = r"E:\实验代码备份\ais data\excel_output_长江\切1\合并后的经纬度航向数据.csv"
    partition_count = 20  # 切分数量
    target_block_index = 1  # 数据块2的索引（从0开始，所以是1）
    output_dir = r"E:\实验代码备份\ais data\excel_output_长江\切1\数据块边界"
    
    print("=" * 60)
    print("数据块边界提取工具")
    print("=" * 60)
    print(f"输入文件: {Path(file_path).name}")
    print(f"切分数量: {partition_count}")
    print(f"目标数据块: {target_block_index + 1}")
    print("=" * 60)
    
    # 获取边界信息
    result = get_block_boundaries(file_path, partition_count, target_block_index)
    
    if result is None:
        print("❌ 获取边界信息失败")
        return
    
    boundaries, block_data = result
    
    # 显示边界信息
    print(f"\n📊 数据块 {boundaries['block_index'] + 1} 边界信息:")
    print(f"  - 数据点数量: {boundaries['total_points']}")
    print(f"  - 经度范围: {boundaries['longitude_min']:.6f} ~ {boundaries['longitude_max']:.6f}")
    print(f"  - 纬度范围: {boundaries['latitude_min']:.6f} ~ {boundaries['latitude_max']:.6f}")
    print(f"  - 经度跨度: {boundaries['longitude_range']:.6f}")
    print(f"  - 纬度跨度: {boundaries['latitude_range']:.6f}")
    
    if 'heading_min' in boundaries:
        print(f"  - 航向范围: {boundaries['heading_min']:.2f} ~ {boundaries['heading_max']:.2f}")
    
    print(f"  - 中心点: ({boundaries['center_point'][0]:.6f}, {boundaries['center_point'][1]:.6f})")
    
    print(f"\n📍 四个角点坐标:")
    for corner_name, (lon, lat) in boundaries['corner_points'].items():
        print(f"  - {corner_name}: ({lon:.6f}, {lat:.6f})")
    
    # 保存数据
    save_boundary_data(boundaries, block_data, output_dir)
    
    # 生成可视化图
    viz_path = Path(output_dir) / f"数据块_{boundaries['block_index'] + 1}_边界可视化.png"
    visualize_block_boundaries(boundaries, block_data, viz_path)
    
    print(f"\n✅ 数据块 {boundaries['block_index'] + 1} 边界信息提取完成！")


if __name__ == "__main__":
    main()
