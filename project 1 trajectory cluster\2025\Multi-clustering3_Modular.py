import numpy as np
import pandas as pd
from sklearn.cluster import KMeans, DBSCAN
from spectralcluster import SpectralClusterer
import os
from scipy.interpolate import splrep, splev
import haversine as hs
from 数据切分模块 import partition_data as dp_partition_data
from visualization_module import AISVisualization

# 设置GUI后端
import matplotlib
matplotlib.use('TkAgg')


class GeoDataProcessor:
    """地理空间数据处理与聚类分析管道（三维：经纬度+航向）- 使用KMeans中心点选取 - 模块化版本"""

    def __init__(self, config):
        """初始化处理器，使用配置字典设置参数"""
        self.config = config
        self.orig_input = None
        self.labels = None
        self.up_data = None
        self.down_data = None
        self.up_centers = None
        self.down_centers = None
        self.up_red = None
        self.down_red = None
        self.splines = {}
        
        # 初始化可视化模块
        self.visualizer = AISVisualization(config)

    def read_and_partition_data(self):
        """读取数据文件并进行切分（三维：经度、纬度、航向）"""
        try:
            # 使用数据切分模块读取和切分数据
            data_list = dp_partition_data(self.config['file_path'], self.config['partition_count'])

            # 初始化存储切分数据的列表
            self.orig_input = []

            for data in data_list:
                if data is not None and data.shape[1] >= 3:
                    # 取前三列作为经度、纬度、航向
                    input_data = data.iloc[:, :3].values

                    # 对航向进行标准化处理（转换为0-360度范围）
                    input_data[:, 2] = input_data[:, 2] % 360

                    self.orig_input.append(input_data)

            if self.orig_input:
                print(f"数据读取并切分完成，共有 {len(self.orig_input)} 组数据")
                return True
            else:
                print("数据切分后为空，请检查输入文件和切分参数。")
                return False
        except Exception as e:
            print(f"数据读取错误: {e}")
            return False

    def perform_clustering(self):
        """执行聚类分析，根据配置选择聚类方法"""
        if not self.orig_input:
            print("数据未读取，无法聚类")
            return False

        method = self.config['cluster_method']
        print(f"使用 {method} 方法进行三维聚类（经纬度+航向）...")

        # 初始化存储聚类结果的列表
        self.labels = []
        self.up_data = []
        self.down_data = []

        for input_data in self.orig_input:
            if method == 'dbscan':
                labels = self._perform_dbscan_clustering(input_data)
            elif method == 'kmeans':
                labels = self._perform_kmeans_clustering(input_data)
            elif method == 'spectral':
                labels = self._perform_spectral_clustering(input_data)
            elif method == 'mix2':
                labels = self._perform_mix2_clustering(input_data)
            elif method == 'mix3':
                labels = self._perform_mix3_clustering(input_data)
            else:
                print(f"未知聚类方法: {method}")
                return False

            # 划分上游(1)和下游(0)
            self.up_data.append(input_data[labels == 1])
            self.down_data.append(input_data[labels == 0])
            self.labels.append(labels)

        print(f"聚类完成: 上游 {sum(len(up) for up in self.up_data)} 点, 下游 {sum(len(down) for down in self.down_data)} 点")
        return True

    def adjust_labels(self, block_index, new_labels):
        """调整指定数据块的聚类标签"""
        if self.labels is not None and 0 <= block_index < len(self.labels):
            self.labels[block_index] = new_labels
            print(f"数据块 {block_index} 的标签已更新。")
        else:
            print(f"无效的数据块索引: {block_index}")

    def _fallback_clustering(self, block):
        """回退聚类方法：按经度中值分二类"""
        order = np.argsort(block[:, 0])
        half = len(order) // 2
        labels_block = np.zeros(len(block), dtype=int)
        labels_block[order[half:]] = 1
        return labels_block

    def _normalize_3d_data(self, data):
        """对三维数据进行标准化，确保各维度的贡献度平衡"""
        # 复制数据避免修改原始数据
        normalized_data = data.copy()

        # 对经纬度使用标准化
        for i in range(2):
            col_std = np.std(normalized_data[:, i])
            if col_std > 0:
                normalized_data[:, i] = (normalized_data[:, i] - np.mean(normalized_data[:, i])) / col_std

        # 对航向进行特殊处理（考虑其周期性）
        # 将航向转换为单位圆上的坐标
        heading_rad = np.radians(normalized_data[:, 2])
        heading_x = np.cos(heading_rad)
        heading_y = np.sin(heading_rad)

        # 用航向的x,y坐标替换原航向，形成4维数据
        extended_data = np.column_stack([
            normalized_data[:, 0],  # 标准化经度
            normalized_data[:, 1],  # 标准化纬度
            heading_x,  # 航向x分量
            heading_y  # 航向y分量
        ])

        return extended_data

    def compute_centroids_kmeans(self):
        """使用KMeans聚类方法计算中心点（改进版）"""
        if not self.up_data or not self.down_data:
            print("请先执行聚类")
            return False

        self.up_centers = []
        self.down_centers = []

        # 获取KMeans中心点计算的配置参数
        centroid_config = self.config.get('centroid_kmeans', {
            'n_clusters': 1,  # 每个数据块只需要一个中心点
            'max_iter': 300,
            'n_init': 10,
            'random_state': 42,
            'min_points': 5  # 最少需要的点数才进行KMeans
        })

        for block_idx, (up_block, down_block) in enumerate(zip(self.up_data, self.down_data)):
            # 处理上游数据的中心点
            if len(up_block) > 0:
                up_center = self._compute_kmeans_centroid(
                    up_block, 
                    centroid_config, 
                    f"上游数据块{block_idx}"
                )
                if up_center is not None:
                    self.up_centers.append(up_center)

            # 处理下游数据的中心点
            if len(down_block) > 0:
                down_center = self._compute_kmeans_centroid(
                    down_block, 
                    centroid_config, 
                    f"下游数据块{block_idx}"
                )
                if down_center is not None:
                    self.down_centers.append(down_center)

        print(f"KMeans中心点计算完成: 上游 {len(self.up_centers)} 点, 下游 {len(self.down_centers)} 点")
        return True

    def _compute_kmeans_centroid(self, data_block, config, block_name):
        """
        使用KMeans为单个数据块计算中心点
        
        Args:
            data_block: 数据块 (n_points, 3) - [经度, 纬度, 航向]
            config: KMeans配置参数
            block_name: 数据块名称（用于日志）
            
        Returns:
            中心点 [经度, 纬度, 航向] 或 None
        """
        if len(data_block) < config['min_points']:
            print(f"{block_name}: 点数不足({len(data_block)} < {config['min_points']})，使用几何中心")
            return self._compute_geometric_centroid(data_block)
        
        try:
            # 准备KMeans输入数据：只使用经纬度进行聚类
            # 航向由于其周期性，在中心点计算中单独处理
            geo_data = data_block[:, :2]  # 只取经纬度
            
            # 执行KMeans聚类找到最佳中心点
            kmeans = KMeans(
                n_clusters=config['n_clusters'],
                max_iter=config['max_iter'],
                n_init=config['n_init'],
                random_state=config['random_state']
            )
            
            kmeans.fit(geo_data)
            geo_centroid = kmeans.cluster_centers_[0]  # 取第一个（也是唯一的）聚类中心
            
            # 找到距离KMeans中心最近的实际数据点
            distances = np.sqrt(np.sum((geo_data - geo_centroid) ** 2, axis=1))
            closest_idx = np.argmin(distances)
            closest_point = data_block[closest_idx]
            
            # 计算该区域内航向的圆形均值
            # 定义"该区域"为距离最近点一定范围内的所有点
            distance_threshold = np.percentile(distances, 30)  # 使用30%分位数作为阈值
            nearby_mask = distances <= distance_threshold
            nearby_headings = data_block[nearby_mask, 2]
            
            # 计算航向的圆形均值
            heading_centroid = self._compute_circular_mean(nearby_headings)
            
            # 组合最终的中心点：KMeans的经纬度 + 圆形均值航向
            final_centroid = np.array([
                geo_centroid[0],  # KMeans计算的经度
                geo_centroid[1],  # KMeans计算的纬度
                heading_centroid  # 圆形均值航向
            ])
            
            print(f"{block_name}: KMeans中心点计算成功，使用了{len(nearby_headings)}个点计算航向")
            return final_centroid
            
        except Exception as e:
            print(f"{block_name}: KMeans计算失败 ({e})，使用几何中心")
            return self._compute_geometric_centroid(data_block)

    def _compute_geometric_centroid(self, data_block):
        """计算几何中心点作为备选方案"""
        centroid = np.zeros(3)
        centroid[0] = np.mean(data_block[:, 0])  # 经度均值
        centroid[1] = np.mean(data_block[:, 1])  # 纬度均值
        centroid[2] = self._compute_circular_mean(data_block[:, 2])  # 航向圆形均值
        return centroid

    def _compute_circular_mean(self, angles_deg):
        """计算角度的圆形均值"""
        angles_rad = np.radians(angles_deg)
        mean_x = np.mean(np.cos(angles_rad))
        mean_y = np.mean(np.sin(angles_rad))
        mean_angle_rad = np.arctan2(mean_y, mean_x)
        return np.degrees(mean_angle_rad) % 360

    def _perform_dbscan_clustering(self, input_data):
        """执行DBSCAN聚类（三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        cut = self.config['dbscan']['cut']
        base_eps = self.config['dbscan']['base_eps']
        min_samples = self.config['dbscan']['min_samples']
        max_eps = self.config['dbscan']['max_eps']

        iteration = int(np.ceil(len(input_data) / cut))
        labels_result = np.zeros(len(input_data), dtype=int)

        def cluster_block_with_dbscan(block):
            normalized_block = self._normalize_3d_data(block)
            eps = base_eps
            while eps <= max_eps:
                preds = DBSCAN(eps=eps, min_samples=min_samples).fit_predict(normalized_block)
                valid = [lbl for lbl in set(preds) if lbl != -1]
                if len(valid) >= 2:
                    sizes = {lbl: np.sum(preds == lbl) for lbl in valid}
                    top2 = sorted(sizes, key=lambda x: sizes[x], reverse=True)[:2]
                    block_labels = np.zeros(len(preds), dtype=int)
                    block_labels[preds == top2[0]] = 1
                    block_labels[preds == top2[1]] = 0
                    return block_labels
                eps *= 0.8
            return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            block_labels = cluster_block_with_dbscan(block)
            if block_labels is None:
                block_labels = self._fallback_clustering(block)
            labels_result[start:end] = block_labels

        return labels_result

    def _perform_kmeans_clustering(self, input_data):
        """执行KMeans聚类（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        n_clusters = self.config['kmeans']['n_clusters']
        init = self.config['kmeans']['init']
        max_iter = self.config['kmeans']['max_iter']
        n_init = self.config['kmeans']['n_init']
        random_state = self.config['kmeans']['random_state']
        cut = self.config['kmeans'].get('cut', 100)

        iteration = int(np.ceil(len(input_data) / cut))
        labels_result = np.zeros(len(input_data), dtype=int)

        def cluster_block_with_kmeans(block):
            if len(block) < n_clusters:
                return None
            try:
                normalized_block = self._normalize_3d_data(block)
                kmeans = KMeans(
                    n_clusters=n_clusters,
                    init=init,
                    max_iter=max_iter,
                    n_init=n_init,
                    random_state=random_state
                )
                preds = kmeans.fit_predict(normalized_block)
                unique_labels = np.unique(preds)
                if len(unique_labels) >= 2:
                    sizes = {lbl: np.sum(preds == lbl) for lbl in unique_labels}
                    top2 = sorted(sizes, key=lambda x: sizes[x], reverse=True)[:2]
                    block_labels = np.zeros(len(preds), dtype=int)
                    block_labels[preds == top2[0]] = 1
                    block_labels[preds == top2[1]] = 0
                    return block_labels
                else:
                    return None
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            block_labels = cluster_block_with_kmeans(block)
            if block_labels is None:
                block_labels = self._fallback_clustering(block)
            labels_result[start:end] = block_labels

        return labels_result

    def _perform_spectral_clustering(self, input_data):
        """执行谱聚类（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        min_clusters = self.config['spectral']['min_clusters']
        max_clusters = self.config['spectral']['max_clusters']
        cut = self.config['spectral'].get('cut', 100)

        iteration = int(np.ceil(len(input_data) / cut))
        labels_result = np.zeros(len(input_data), dtype=int)

        def cluster_block_with_spectral(block):
            if len(block) < max(min_clusters, 3):
                return None
            try:
                normalized_block = self._normalize_3d_data(block)
                spectral = SpectralClusterer(
                    min_clusters=min_clusters,
                    max_clusters=max_clusters,
                )
                preds = spectral.predict(normalized_block)
                unique_labels = np.unique(preds)
                if len(unique_labels) >= 2:
                    sizes = {lbl: np.sum(preds == lbl) for lbl in unique_labels}
                    top2 = sorted(sizes, key=lambda x: sizes[x], reverse=True)[:2]
                    block_labels = np.zeros(len(preds), dtype=int)
                    block_labels[preds == top2[0]] = 1
                    block_labels[preds == top2[1]] = 0
                    return block_labels
                else:
                    return None
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            block_labels = cluster_block_with_spectral(block)
            if block_labels is None:
                block_labels = self._fallback_clustering(block)
            labels_result[start:end] = block_labels

        return labels_result

    def _perform_mix2_clustering(self, input_data):
        """执行KMeans和谱聚类的混合方法（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        cut = self.config.get('mix_cut', 100)
        iteration = int(np.ceil(len(input_data) / cut))
        labels_result = np.zeros(len(input_data), dtype=int)

        kmeans_config = self.config['kmeans']
        spectral_config = self.config['spectral']

        def cluster_block_with_mix2(block):
            if len(block) < 3:
                return None
            try:
                normalized_block = self._normalize_3d_data(block)

                # 执行KMeans
                kmeans = KMeans(
                    n_clusters=kmeans_config['n_clusters'],
                    init=kmeans_config['init'],
                    max_iter=kmeans_config['max_iter'],
                    n_init=kmeans_config['n_init'],
                    random_state=kmeans_config['random_state']
                )
                kmeans_labels = kmeans.fit_predict(normalized_block)

                # 执行谱聚类
                spectral = SpectralClusterer(
                    min_clusters=spectral_config['min_clusters'],
                    max_clusters=spectral_config['max_clusters'],
                )
                spectral_labels = spectral.predict(normalized_block)

                # 合并结果
                block_labels = np.zeros_like(kmeans_labels)
                block_labels[(kmeans_labels == 1) & (spectral_labels == 1)] = 1
                return block_labels
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            block_labels = cluster_block_with_mix2(block)
            if block_labels is None:
                block_labels = self._fallback_clustering(block)
            labels_result[start:end] = block_labels

        return labels_result

    def _perform_mix3_clustering(self, input_data):
        """执行KMeans、谱聚类和DBSCAN的混合方法（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        cut = self.config.get('mix_cut', 100)
        iteration = int(np.ceil(len(input_data) / cut))
        labels_result = np.zeros(len(input_data), dtype=int)

        kmeans_config = self.config['kmeans']
        spectral_config = self.config['spectral']
        dbscan_config = self.config['dbscan']

        def cluster_block_with_mix3(block):
            if len(block) < 3:
                return None
            try:
                normalized_block = self._normalize_3d_data(block)

                # 执行KMeans
                kmeans = KMeans(
                    n_clusters=kmeans_config['n_clusters'],
                    init=kmeans_config['init'],
                    max_iter=kmeans_config['max_iter'],
                    n_init=kmeans_config['n_init'],
                    random_state=kmeans_config['random_state']
                )
                kmeans_labels = kmeans.fit_predict(normalized_block)

                # 执行谱聚类
                spectral = SpectralClusterer(
                    min_clusters=spectral_config['min_clusters'],
                    max_clusters=spectral_config['max_clusters'],
                )
                spectral_labels = spectral.predict(normalized_block)

                # 执行DBSCAN
                dbscan_labels = DBSCAN(
                    eps=dbscan_config['base_eps'],
                    min_samples=dbscan_config['min_samples']
                ).fit_predict(normalized_block)

                # 合并结果
                valid_dbscan = (dbscan_labels != -1)
                block_labels = np.zeros_like(kmeans_labels)
                block_labels[(kmeans_labels == 1) & (spectral_labels == 1) & (dbscan_labels == 1) & valid_dbscan] = 1
                return block_labels
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            block_labels = cluster_block_with_mix3(block)
            if block_labels is None:
                block_labels = self._fallback_clustering(block)
            labels_result[start:end] = block_labels

        return labels_result

    def fit_splines(self):
        """拟合样条曲线（只对经纬度进行拟合，航向用于显示）"""
        if self.up_red is None or self.down_red is None:
            print("请先计算中心点")
            return False

        k = self.config['spline_degree']

        self.splines = {}
        for name, pts in [('up', self.up_red), ('down', self.down_red)]:
            if pts.shape[0] > k:
                t = np.arange(len(pts))
                # 只对经纬度进行样条拟合
                self.splines[name] = (
                    splrep(t, pts[:, 0], s=0),  # 经度样条
                    splrep(t, pts[:, 1], s=0),  # 纬度样条
                    t.min(),
                    t.max()
                )
            else:
                print(f"警告: {name} 数据点不足，无法拟合样条曲线")

        return True

    def filter_noise_points(self):
        """基于局部密度分析筛选噪点，使用KD-Tree计算近邻"""
        from sklearn.neighbors import KDTree

        if not self.orig_input or not self.labels:
            print("数据不完整，无法进行噪点筛选")
            return False

        # 获取配置参数
        n_neighbors = self.config['noise_filter']['n_neighbors']
        min_ratio = self.config['noise_filter']['min_ratio']
        # 注意：不再使用 angle_weight，噪点筛选只基于地理位置距离

        # 用于存储筛选后的数据
        filtered_up_data = []
        filtered_down_data = []

        # 遍历每个数据块
        for block_idx in range(len(self.orig_input)):
            input_data = self.orig_input[block_idx]
            labels = self.labels[block_idx]

            if len(input_data) < n_neighbors:
                print(f"数据块 {block_idx} 的点数少于所需邻居数，跳过噪点筛选")
                filtered_up_data.append(input_data[labels == 1])
                filtered_down_data.append(input_data[labels == 0])
                continue

            # 准备用于KD-Tree的特征数据：只使用经纬度，不考虑航向
            features = np.column_stack([
                input_data[:, 0],  # 经度
                input_data[:, 1],  # 纬度
            ])

            # 构建KD-Tree（只基于地理位置）
            tree = KDTree(features)

            # 查找每个点的最近邻
            distances, indices = tree.query(features, k=n_neighbors)

            # 计算每个点邻域内同标签点的比例
            is_noise = np.zeros(len(input_data), dtype=bool)
            for i in range(len(input_data)):
                neighbor_labels = labels[indices[i]]
                same_label_ratio = np.sum(neighbor_labels == labels[i]) / n_neighbors
                is_noise[i] = same_label_ratio < min_ratio

            # 分离非噪点数据
            up_mask = (labels == 1) & ~is_noise
            down_mask = (labels == 0) & ~is_noise

            filtered_up = input_data[up_mask]
            filtered_down = input_data[down_mask]

            # 存储筛选后的数据
            filtered_up_data.append(filtered_up)
            filtered_down_data.append(filtered_down)

            # 打印筛选结果
            removed_up = np.sum(labels == 1) - len(filtered_up)
            removed_down = np.sum(labels == 0) - len(filtered_down)
            print(f"数据块 {block_idx}: 剔除 {removed_up} 个上游噪点, {removed_down} 个下游噪点")

        # 更新数据
        self.up_data = filtered_up_data
        self.down_data = filtered_down_data

        total_up = sum(len(up) for up in filtered_up_data)
        total_down = sum(len(down) for down in filtered_down_data)
        print(f"筛选完成: 保留上游 {total_up} 点, 下游 {total_down} 点")

        # 重新使用KMeans方法计算中心点
        self.up_centers = []
        self.down_centers = []
        self.up_red = []
        self.down_red = []

        # 获取KMeans中心点计算的配置参数
        centroid_config = self.config.get('centroid_kmeans', {
            'n_clusters': 1,
            'max_iter': 300,
            'n_init': 10,
            'random_state': 42,
            'min_points': 5
        })

        for block_idx, (up_block, down_block) in enumerate(zip(filtered_up_data, filtered_down_data)):
            # 处理上游数据的中心点
            if len(up_block) > 0:
                up_center = self._compute_kmeans_centroid(
                    up_block,
                    centroid_config,
                    f"筛选后上游数据块{block_idx}"
                )
                if up_center is not None:
                    self.up_centers.append(up_center)
                    self.up_red.append(up_center)

            # 处理下游数据的中心点
            if len(down_block) > 0:
                down_center = self._compute_kmeans_centroid(
                    down_block,
                    centroid_config,
                    f"筛选后下游数据块{block_idx}"
                )
                if down_center is not None:
                    self.down_centers.append(down_center)
                    self.down_red.append(down_center)

        # 转换为numpy数组
        self.up_red = np.array(self.up_red) if self.up_red else np.empty((0, 3))
        self.down_red = np.array(self.down_red) if self.down_red else np.empty((0, 3))

        print(f"KMeans中心点重新计算完成: 上游 {len(self.up_centers)} 点, 下游 {len(self.down_centers)} 点")

        # 使用可视化模块生成散点图
        self.visualizer.plot_filtered_scatter(
            self.up_data,
            self.down_data,
            self.up_centers,
            self.down_centers,
            self.config['save_dir']
        )

        return True

    def run_full_pipeline(self):
        """运行完整处理管道（使用KMeans中心点方法 + 模块化可视化）"""
        print("开始运行三维数据处理与聚类分析管道（经纬度+航向）- KMeans中心点模块化版本...")

        if not self.read_and_partition_data():
            print("数据读取失败，终止处理")
            return False

        if not self.perform_clustering():
            print("聚类分析失败，终止处理")
            return False

        if not self.compute_centroids_kmeans():
            print("KMeans中心点计算失败，终止处理")
            return False

        # 设置up_red和down_red为KMeans计算的中心点
        self.up_red = np.array(self.up_centers) if self.up_centers else np.empty((0, 3))
        self.down_red = np.array(self.down_centers) if self.down_centers else np.empty((0, 3))

        if not self.filter_noise_points():
            print("噪点筛选失败，终止处理")
            return False

        # 不需要再次调用compute_centroids_kmeans，因为filter_noise_points已经重新计算了中心点
        if not self.fit_splines():
            print("样条拟合失败，终止处理")
            return False

        # 使用可视化模块生成所有图片
        self.visualizer.visualize_all_results(
            self.orig_input,
            self.labels,
            self.up_red,
            self.down_red,
            self.splines
        )

        # 使用可视化模块保存标签数据
        if not self.visualizer.save_labeled_data(
            self.up_data,
            self.down_data,
            self.config['save_dir']
        ):
            print("标签数据保存失败，终止处理")
            return False

        print(f"KMeans中心点模块化处理完成！所有结果已保存至: {self.config['save_dir']}")
        return True


# 配置参数集中定义
cluster_method = 'dbscan'  # 可选: 'dbscan', 'kmeans', 'spectral', 'mix2', 'mix3'
save_dir = os.path.join(r'E:\实验代码备份\project 1 trajectory cluster\pictures\3', f'{cluster_method}_kmeans_modular')

config = {
    'file_path': r"E:\实验代码备份\ais data\excel_output_长江\all\整合数据_经纬度航向.csv",
    'save_dir': save_dir,
    'cluster_method': cluster_method,
    'partition_count': 20,  # 切分数量

    # KMeans中心点计算参数（新增）
    'centroid_kmeans': {
        'n_clusters': 1,        # 每个数据块只需要一个中心点
        'max_iter': 300,        # 最大迭代次数
        'n_init': 10,           # 不同初始化的运行次数
        'random_state': 42,     # 随机种子，确保结果可重现
        'min_points': 5         # 最少需要的点数才进行KMeans，否则使用几何中心
    },

    # 噪点筛选参数
    'noise_filter': {
        'n_neighbors': 100,  # 计算局部密度时考虑的邻居数量
        'min_ratio': 0.5,   # 邻域内同标签点的最小比例
    },

    # DBSCAN参数（针对三维数据调整）
    'dbscan': {
        'cut': 10000,
        'base_eps': 0.3,
        'min_samples': 5,
        'max_eps': 1.5
    },

    # KMeans参数
    'kmeans': {
        'n_clusters': 2,
        'init': 'k-means++',
        'max_iter': 500,
        'n_init': 10,
        'random_state': 0,
        'cut': 10000
    },

    # 谱聚类参数
    'spectral': {
        'min_clusters': 2,
        'max_clusters': 2,
        'cut': 10000
    },

    # 混合方法的分块大小
    'mix_cut': 100,

    # 样条拟合参数
    'spline_degree': 3,

    # 新增参数
    'target_sheet': 'Sheet1'  # 假设目标sheet名为'Sheet1'
}

# 主程序
if __name__ == "__main__":
    # 创建处理器实例
    processor = GeoDataProcessor(config)

    # 运行完整管道
    if processor.run_full_pipeline():
        print("KMeans中心点模块化版本程序成功完成！")
    else:
        print("程序执行过程中出现错误！")
