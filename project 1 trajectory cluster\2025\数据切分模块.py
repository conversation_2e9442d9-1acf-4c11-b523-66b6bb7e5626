import pandas as pd

def read_data(file_path):
    """
    读取包含经纬度数据的CSV文件。
    """
    df = pd.read_csv(file_path)
    if 'latitude' not in df.columns or 'longitude' not in df.columns:
        raise ValueError("数据中必须包含 'latitude' 和 'longitude' 列。")
    return df

def get_bounds(df):
    """
    获取经纬度的最大最小值。
    """
    lat_max = df['latitude'].max()
    lat_min = df['latitude'].min()
    lon_max = df['longitude'].max()
    lon_min = df['longitude'].min()
    return lat_max, lat_min, lon_max, lon_min

def construct_rectangle(lat_max, lat_min, lon_max, lon_min):
    """
    构建矩形区域并计算高度和宽度。
    """
    height = lat_max - lat_min
    width = lon_max - lon_min
    return height, width

def determine_start_corner(df, lat_max, lon_min, lon_max, lat_step, lon_step):
    """
    判断从左上角或右上角开始划分区域。
    """
    # 左上角区域
    lat_upper = lat_max
    lat_lower = lat_max - lat_step
    lon_left = lon_min
    lon_right = lon_min + lon_step
    data_left = df[(df['latitude'] <= lat_upper) & (df['latitude'] >= lat_lower) &
                   (df['longitude'] >= lon_left) & (df['longitude'] <= lon_right)]

    # 右上角区域
    lon_left_r = lon_max - lon_step
    lon_right_r = lon_max
    data_right = df[(df['latitude'] <= lat_upper) & (df['latitude'] >= lat_lower) &
                    (df['longitude'] >= lon_left_r) & (df['longitude'] <= lon_right_r)]

    if not data_left.empty:
        return 'left'
    elif not data_right.empty:
        return 'right'
    else:
        return None

def divide_and_process(df, lat_max, lat_min, lon_max, lon_min, n, start_corner):
    """
    划分矩形区域并在每个区域内读取新数据。
    """
    lat_step = (lat_max - lat_min) / n
    lon_step = (lon_max - lon_min) / n
    collected_data = pd.DataFrame()
    data_list = []

    for i in range(1, n + 1):
        if start_corner == 'left':
            lat_upper = lat_max
            lat_lower = lat_max - lat_step * i
            lon_left = lon_min
            lon_right = lon_min + lon_step * i
        else:
            lat_upper = lat_max
            lat_lower = lat_max - lat_step * i
            lon_left = lon_max - lon_step * i
            lon_right = lon_max

        region_data = df[(df['latitude'] <= lat_upper) & (df['latitude'] >= lat_lower) &
                         (df['longitude'] >= lon_left) & (df['longitude'] <= lon_right)]

        # 去除已收集的数据
        new_data = region_data[~region_data.index.isin(collected_data.index)]
        collected_data = pd.concat([collected_data, new_data])
        data_list.append(new_data)

    return data_list

def partition_data(file_path, n):
    """
    主函数，调用其他函数完成数据划分和处理。
    """
    # 读取数据
    df = read_data(file_path)

    # 获取边界和矩形尺寸
    lat_max, lat_min, lon_max, lon_min = get_bounds(df)
    height, width = construct_rectangle(lat_max, lat_min, lon_max, lon_min)
    lat_step = height / n
    lon_step = width / n

    # 判断起始角
    start_corner = determine_start_corner(df, lat_max, lon_min, lon_max, lat_step, lon_step)
    if start_corner is None:
        print("无法在左上角或右上角找到数据，尝试扩大区域或检查数据。")
        return []

    # 划分区域并处理数据
    data_list = divide_and_process(df, lat_max, lat_min, lon_max, lon_min, n, start_corner)

    return data_list





    #-------------------------------------------调用----------------------------------------------
    import data_partition as dp

    # 直接使用数据列表，不打印统计信息
    # data_list = dp.partition_data("path/to/your/data.csv", 5)

    # # 例如：保存每个区域的数据到单独的CSV文件
    # for i, data in enumerate(data_list):
    #     data.to_csv(f"region_{i+1}.csv", index=False)