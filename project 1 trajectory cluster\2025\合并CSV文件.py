"""
CSV文件合并工具
将多个CSV文件合并为一个文件
"""

import pandas as pd
import os
from pathlib import Path


def merge_csv_files():
    """合并指定的CSV文件"""
    
    # 要合并的文件列表
    csv_files = [
        r"E:\实验代码备份\ais data\excel_output_长江\切3\经纬度航向提取_1.csv",
        r"E:\实验代码备份\ais data\excel_output_长江\切3\经纬度航向提取_2.csv",
        r"E:\实验代码备份\ais data\excel_output_长江\切3\经纬度航向提取_3.csv",
        r"E:\实验代码备份\ais data\excel_output_长江\切3\经纬度航向提取_4.csv",
        r"E:\实验代码备份\ais data\excel_output_长江\切3\经纬度航向提取_5.csv"
    ]

    # 输出文件路径
    output_file = r"E:\实验代码备份\ais data\excel_output_长江\切1\合并后的经纬度航向数据.csv"
    
    print("=" * 60)
    print("CSV文件合并工具")
    print("=" * 60)
    
    # 存储所有数据
    all_dataframes = []
    total_records = 0
    
    # 逐个读取文件
    for i, file_path in enumerate(csv_files, 1):
        print(f"[{i}/{len(csv_files)}] 正在读取: {Path(file_path).name}")
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"  ⚠️  文件不存在，跳过: {Path(file_path).name}")
                continue
            
            # 读取CSV文件
            df = pd.read_csv(file_path)
            
            if df.empty:
                print(f"  ⚠️  文件为空，跳过: {Path(file_path).name}")
                continue
            
            # 添加来源文件信息
            df['source_file'] = Path(file_path).name
            
            all_dataframes.append(df)
            total_records += len(df)
            
            print(f"  ✅ 成功读取 {len(df)} 条记录")
            
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
            continue
    
    # 检查是否有数据
    if not all_dataframes:
        print("\n❌ 没有成功读取任何文件，合并失败")
        return False
    
    print(f"\n📊 总计读取 {len(all_dataframes)} 个文件，{total_records} 条记录")
    
    # 合并所有数据
    print("正在合并数据...")
    try:
        merged_df = pd.concat(all_dataframes, ignore_index=True)
        
        # 确保输出目录存在
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存合并后的数据
        merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 显示结果统计
        print("\n" + "=" * 60)
        print("🎉 合并完成！")
        print("=" * 60)
        print(f"📄 输出文件: {output_path.name}")
        print(f"📁 保存位置: {output_path.parent}")
        print(f"📊 合并后记录数: {len(merged_df)}")
        print(f"📈 文件大小: {output_path.stat().st_size / 1024:.2f} KB")
        
        # 显示列信息
        print(f"📋 数据列: {list(merged_df.columns)}")
        
        # 显示来源文件统计
        if 'source_file' in merged_df.columns:
            source_stats = merged_df['source_file'].value_counts()
            print("\n📂 来源文件统计:")
            for source, count in source_stats.items():
                print(f"  - {source}: {count} 条记录")
        
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ 合并过程中发生错误: {e}")
        return False


def main():
    """主函数"""
    try:
        success = merge_csv_files()
        
        if success:
            print("\n✅ 程序执行成功")
        else:
            print("\n❌ 程序执行失败")
            
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序执行")
    except Exception as e:
        print(f"\n❌ 程序执行过程中发生错误: {e}")
    
    # 等待用户按键退出
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
