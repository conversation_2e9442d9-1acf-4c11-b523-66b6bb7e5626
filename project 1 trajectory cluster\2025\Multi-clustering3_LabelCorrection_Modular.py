import numpy as np
import pandas as pd
from sklearn.cluster import KMeans, DBSCAN
from spectralcluster import SpectralClusterer
import os
from pathlib import Path
from scipy.interpolate import splrep, splev
import haversine as hs
from 数据切分模块2 import partition_data as dp_partition_data
from visualization_module import AISVisualization

# 设置GUI后端
import matplotlib
matplotlib.use('TkAgg')


class GeoDataProcessor:
    """地理空间数据处理与聚类分析管道（三维：经纬度+航向）- 带标签修正功能 - 模块化版本"""

    def __init__(self, config):
        """初始化处理器，使用配置字典设置参数"""
        self.config = config
        self.orig_input = None
        self.labels = None
        self.up_data = None
        self.down_data = None
        self.up_centers = None
        self.down_centers = None
        self.up_red = None
        self.down_red = None
        self.splines = {}
        
        # 标签修正相关
        self.label_corrections = {}  # 存储需要修正的数据块标签
        self.corrected_blocks = set()  # 记录已修正的数据块

        # 单独聚类设置相关
        self.custom_clustering = {}  # 存储特定数据块的聚类配置
        self.custom_clustered_blocks = set()  # 记录使用自定义聚类的数据块

        # 功能开关
        self.enable_label_correction = True  # 是否启用标签修正
        self.enable_custom_clustering = True  # 是否启用单独聚类设置
        
        # 初始化可视化模块
        self.visualizer = AISVisualization(config)

    def read_and_partition_data(self):
        """读取数据文件并进行切分（三维：经度、纬度、航向）- 支持动态切分总量"""
        try:
            # 获取切分配置
            target_segments = self.config['partition_count']

            # 获取自定义段落宽度配置（如果有）
            custom_lengths = self.config.get('custom_partition_lengths', None)

            # 获取经纬度列索引配置
            longitude_col_index = self.config.get('longitude_col_index', 0)
            latitude_col_index = self.config.get('latitude_col_index', 1)

            print(f"🔄 开始数据切分...")
            print(f"   - 目标段落数量: {target_segments}")
            if custom_lengths:
                print(f"   - 自定义段落宽度: {custom_lengths}")

            # 使用新的数据切分模块读取和切分数据
            data_list = dp_partition_data(
                file_path=self.config['file_path'],
                target_segments=target_segments,
                custom_lengths=custom_lengths,
                longitude_col_index=longitude_col_index,
                latitude_col_index=latitude_col_index
            )

            # 初始化存储切分数据的列表
            self.orig_input = []

            for i, data in enumerate(data_list):
                if data is not None and not data.empty and data.shape[1] >= 3:
                    # 取前三列作为经度、纬度、航向
                    input_data = data.iloc[:, :3].values

                    # 对航向进行标准化处理（转换为0-360度范围）
                    if input_data.shape[1] > 2:  # 确保有航向列
                        input_data[:, 2] = input_data[:, 2] % 360

                    self.orig_input.append(input_data)
                    print(f"   段落 {i+1}: {len(input_data)} 个数据点")
                elif data is not None and not data.empty:
                    print(f"   段落 {i+1}: 数据列数不足，跳过")
                else:
                    print(f"   段落 {i+1}: 无数据，跳过")

            if self.orig_input:
                total_points = sum(len(data) for data in self.orig_input)
                print(f"✅ 数据读取并切分完成，共有 {len(self.orig_input)} 组数据，总计 {total_points} 个数据点")
                return True
            else:
                print("❌ 数据切分后为空，请检查输入文件和切分参数。")
                return False
        except Exception as e:
            print(f"❌ 数据读取错误: {e}")
            return False

    def perform_clustering(self):
        """执行聚类分析，根据配置选择聚类方法（支持单独聚类设置）"""
        if not self.orig_input:
            print("数据未读取，无法聚类")
            return False

        method = self.config['cluster_method']
        print(f"使用 {method} 方法进行三维聚类（经纬度+航向）...")

        if self.custom_clustering:
            print(f"检测到 {len(self.custom_clustering)} 个数据块使用自定义聚类配置")

        # 初始化存储聚类结果的列表
        self.labels = []
        self.up_data = []
        self.down_data = []

        for block_idx, input_data in enumerate(self.orig_input):
            # 检查是否有自定义聚类配置
            if self.enable_custom_clustering and block_idx in self.custom_clustering:
                custom_config = self.custom_clustering[block_idx]
                custom_method = custom_config['method']
                custom_params = custom_config.get('params', {})

                print(f"数据块 {block_idx + 1}: 使用自定义聚类方法 {custom_method}")
                labels = self._perform_custom_clustering(input_data, custom_method, custom_params)
                self.custom_clustered_blocks.add(block_idx)
            else:
                # 使用默认聚类方法
                if method == 'dbscan':
                    labels = self._perform_dbscan_clustering(input_data)
                elif method == 'kmeans':
                    labels = self._perform_kmeans_clustering(input_data)
                elif method == 'spectral':
                    labels = self._perform_spectral_clustering(input_data)
                elif method == 'mix2':
                    labels = self._perform_mix2_clustering(input_data)
                elif method == 'mix3':
                    labels = self._perform_mix3_clustering(input_data)
                else:
                    print(f"未知聚类方法: {method}")
                    return False

            # 划分上游(1)和下游(0)
            self.up_data.append(input_data[labels == 1])
            self.down_data.append(input_data[labels == 0])
            self.labels.append(labels)

        total_up = sum(len(up) for up in self.up_data)
        total_down = sum(len(down) for down in self.down_data)
        print(f"聚类完成: 上游 {total_up} 点, 下游 {total_down} 点")

        if self.custom_clustered_blocks:
            print(f"使用自定义聚类的数据块: {sorted(list(self.custom_clustered_blocks))}")

        return True

    def set_custom_clustering(self, custom_configs):
        """
        设置特定数据块的自定义聚类配置

        Args:
            custom_configs: 字典，格式为 {block_index: {'method': str, 'params': dict}}

        Example:
            processor.set_custom_clustering({
                17: {
                    'method': 'kmeans',
                    'params': {
                        'n_clusters': 3,
                        'max_iter': 500,
                        'cut': 5000
                    }
                },
                18: {
                    'method': 'dbscan',
                    'params': {
                        'base_eps': 0.5,
                        'min_samples': 10
                    }
                }
            })
        """
        self.custom_clustering = custom_configs
        print(f"设置自定义聚类配置: {len(custom_configs)} 个数据块")
        for block_idx, config in custom_configs.items():
            method = config['method']
            params = config.get('params', {})
            print(f"  - 数据块 {block_idx + 1}: 使用 {method} 方法")
            if params:
                print(f"    参数: {params}")

    def _perform_custom_clustering(self, input_data, method, custom_params):
        """
        使用自定义参数执行聚类

        Args:
            input_data: 输入数据
            method: 聚类方法名称
            custom_params: 自定义参数字典

        Returns:
            聚类标签
        """
        # 临时保存原始配置
        original_config = {}

        try:
            # 应用自定义参数
            if method in self.config:
                original_config = self.config[method].copy()
                self.config[method].update(custom_params)

            # 执行对应的聚类方法
            if method == 'dbscan':
                labels = self._perform_dbscan_clustering(input_data)
            elif method == 'kmeans':
                labels = self._perform_kmeans_clustering(input_data)
            elif method == 'spectral':
                labels = self._perform_spectral_clustering(input_data)
            elif method == 'mix2':
                labels = self._perform_mix2_clustering(input_data)
            elif method == 'mix3':
                labels = self._perform_mix3_clustering(input_data)
            else:
                print(f"未知的自定义聚类方法: {method}")
                return self._fallback_clustering(input_data)

            return labels

        finally:
            # 恢复原始配置
            if original_config and method in self.config:
                self.config[method] = original_config

    def set_feature_switches(self, enable_label_correction=True, enable_custom_clustering=True):
        """
        设置功能开关

        Args:
            enable_label_correction: 是否启用标签修正功能
            enable_custom_clustering: 是否启用自定义聚类功能
        """
        self.enable_label_correction = enable_label_correction
        self.enable_custom_clustering = enable_custom_clustering

        print("功能开关设置:")
        print(f"  - 标签修正功能: {'启用' if enable_label_correction else '禁用'}")
        print(f"  - 自定义聚类功能: {'启用' if enable_custom_clustering else '禁用'}")

    def set_label_corrections(self, corrections):
        """
        设置需要修正的数据块标签
        
        Args:
            corrections: 字典，格式为 {block_index: 'swap'} 或 {block_index: [new_labels]}
                        - 'swap': 交换该数据块的标签（0变1，1变0）
                        - [new_labels]: 直接指定新的标签数组
        
        Example:
            processor.set_label_corrections({
                18: 'swap',  # 第19组数据（索引18）标签交换
                19: 'swap'   # 第20组数据（索引19）标签交换
            })
        """
        self.label_corrections = corrections
        print(f"设置标签修正: {len(corrections)} 个数据块需要修正")
        for block_idx, correction in corrections.items():
            if correction == 'swap':
                print(f"  - 数据块 {block_idx + 1}: 标签交换")
            else:
                print(f"  - 数据块 {block_idx + 1}: 自定义标签")

    def apply_label_corrections(self):
        """应用标签修正"""
        if not self.enable_label_correction:
            print("标签修正功能已禁用，跳过")
            return True

        if not self.label_corrections:
            print("没有需要修正的标签")
            return True

        if not self.labels:
            print("请先执行聚类")
            return False

        print("开始应用标签修正...")
        
        for block_idx, correction in self.label_corrections.items():
            if block_idx >= len(self.labels):
                print(f"警告: 数据块索引 {block_idx} 超出范围，跳过")
                continue
                
            original_labels = self.labels[block_idx].copy()
            
            if correction == 'swap':
                # 交换标签：0变1，1变0
                corrected_labels = 1 - original_labels
                self.labels[block_idx] = corrected_labels
                
                # 统计修正结果
                swapped_count = len(corrected_labels)
                print(f"  数据块 {block_idx + 1}: 交换了 {swapped_count} 个点的标签")
                
            elif isinstance(correction, (list, np.ndarray)):
                # 直接指定新标签
                if len(correction) != len(original_labels):
                    print(f"警告: 数据块 {block_idx} 的新标签长度不匹配，跳过")
                    continue
                    
                self.labels[block_idx] = np.array(correction)
                changed_count = np.sum(original_labels != correction)
                print(f"  数据块 {block_idx + 1}: 修改了 {changed_count} 个点的标签")
            
            self.corrected_blocks.add(block_idx)

        # 重新划分上游和下游数据
        self.up_data = []
        self.down_data = []
        
        for i, (input_data, labels) in enumerate(zip(self.orig_input, self.labels)):
            self.up_data.append(input_data[labels == 1])
            self.down_data.append(input_data[labels == 0])

        total_up = sum(len(up) for up in self.up_data)
        total_down = sum(len(down) for down in self.down_data)
        print(f"标签修正完成: 上游 {total_up} 点, 下游 {total_down} 点")
        print(f"已修正数据块: {sorted(list(self.corrected_blocks))}")
        
        return True

    def preview_label_corrections(self, corrections):
        """
        预览标签修正的效果，不实际应用
        
        Args:
            corrections: 需要预览的修正配置
        """
        if not self.labels:
            print("请先执行聚类")
            return
            
        print("=== 标签修正预览 ===")
        
        for block_idx, correction in corrections.items():
            if block_idx >= len(self.labels):
                print(f"数据块 {block_idx + 1}: 索引超出范围")
                continue
                
            original_labels = self.labels[block_idx]
            original_up = np.sum(original_labels == 1)
            original_down = np.sum(original_labels == 0)
            
            if correction == 'swap':
                new_up = original_down
                new_down = original_up
                print(f"数据块 {block_idx + 1} (交换标签):")
                print(f"  修正前: 上游 {original_up} 点, 下游 {original_down} 点")
                print(f"  修正后: 上游 {new_up} 点, 下游 {new_down} 点")
                
            elif isinstance(correction, (list, np.ndarray)):
                new_labels = np.array(correction)
                new_up = np.sum(new_labels == 1)
                new_down = np.sum(new_labels == 0)
                changed_count = np.sum(original_labels != new_labels)
                print(f"数据块 {block_idx + 1} (自定义标签):")
                print(f"  修正前: 上游 {original_up} 点, 下游 {original_down} 点")
                print(f"  修正后: 上游 {new_up} 点, 下游 {new_down} 点")
                print(f"  变更点数: {changed_count}")

    def adjust_labels(self, block_index, new_labels):
        """调整指定数据块的聚类标签（保留原有接口）"""
        if self.labels is not None and 0 <= block_index < len(self.labels):
            self.labels[block_index] = new_labels
            print(f"数据块 {block_index} 的标签已更新。")
        else:
            print(f"无效的数据块索引: {block_index}")

    def _fallback_clustering(self, block):
        """回退聚类方法：按经度中值分二类"""
        order = np.argsort(block[:, 0])
        half = len(order) // 2
        labels_block = np.zeros(len(block), dtype=int)
        labels_block[order[half:]] = 1
        return labels_block

    def _normalize_3d_data(self, data):
        """对三维数据进行标准化，确保各维度的贡献度平衡"""
        # 复制数据避免修改原始数据
        normalized_data = data.copy()

        # 对经纬度使用标准化
        for i in range(2):
            col_std = np.std(normalized_data[:, i])
            if col_std > 0:
                normalized_data[:, i] = (normalized_data[:, i] - np.mean(normalized_data[:, i])) / col_std

        # 对航向进行特殊处理（考虑其周期性）
        # 将航向转换为单位圆上的坐标
        heading_rad = np.radians(normalized_data[:, 2])
        heading_x = np.cos(heading_rad)
        heading_y = np.sin(heading_rad)

        # 用航向的x,y坐标替换原航向，形成4维数据
        extended_data = np.column_stack([
            normalized_data[:, 0],  # 标准化经度
            normalized_data[:, 1],  # 标准化纬度
            heading_x,  # 航向x分量
            heading_y  # 航向y分量
        ])

        return extended_data

    def compute_centroids_kmeans(self):
        """使用KMeans聚类方法计算中心点（改进版）"""
        if not self.up_data or not self.down_data:
            print("请先执行聚类")
            return False

        self.up_centers = []
        self.down_centers = []

        # 获取KMeans中心点计算的配置参数
        centroid_config = self.config.get('centroid_kmeans', {
            'n_clusters': 1,  # 每个数据块只需要一个中心点
            'max_iter': 300,
            'n_init': 10,
            'random_state': 42,
            'min_points': 5  # 最少需要的点数才进行KMeans
        })

        for block_idx, (up_block, down_block) in enumerate(zip(self.up_data, self.down_data)):
            # 处理上游数据的中心点
            if len(up_block) > 0:
                up_center = self._compute_kmeans_centroid(
                    up_block,
                    centroid_config,
                    f"上游数据块{block_idx}",
                    block_idx,
                    'up'
                )
                if up_center is not None:
                    # 处理单个中心点或多个中心点的情况
                    if isinstance(up_center, list):
                        self.up_centers.extend(up_center)  # 添加多个中心点
                    else:
                        self.up_centers.append(up_center)  # 添加单个中心点

            # 处理下游数据的中心点
            if len(down_block) > 0:
                down_center = self._compute_kmeans_centroid(
                    down_block,
                    centroid_config,
                    f"下游数据块{block_idx}",
                    block_idx,
                    'down'
                )
                if down_center is not None:
                    # 处理单个中心点或多个中心点的情况
                    if isinstance(down_center, list):
                        self.down_centers.extend(down_center)  # 添加多个中心点
                    else:
                        self.down_centers.append(down_center)  # 添加单个中心点

        print(f"KMeans中心点计算完成: 上游 {len(self.up_centers)} 点, 下游 {len(self.down_centers)} 点")

        # 🎯 根据配置调整中心点连接顺序
        print("\n=== 调整中心点连接顺序 ===")
        self.up_centers, self.down_centers = self._apply_centroid_connection_order(
            self.up_centers, self.down_centers
        )
        print(f"连接顺序调整完成: 上游 {len(self.up_centers)} 点, 下游 {len(self.down_centers)} 点")

        # 保存中心点到CSV文件
        self.save_centroids_to_csv()

        # 使用可视化模块生成散点图（基于筛选后的数据和计算的中心点）
        self.visualizer.plot_filtered_scatter(
            self.up_data,
            self.down_data,
            self.up_centers,
            self.down_centers,
            self.config['save_dir']
        )

        return True

    def _compute_kmeans_centroid(self, data_block, config, block_name, block_idx, direction):
        """
        使用KMeans为单个数据块计算中心点，支持自定义策略

        Args:
            data_block: 数据块 (n_points, 3) - [经度, 纬度, 航向]
            config: KMeans配置参数
            block_name: 数据块名称（用于日志）
            block_idx: 数据块索引
            direction: 方向 ('up' 或 'down')

        Returns:
            中心点 [经度, 纬度, 航向] 或 None
        """
        # 检查是否有自定义中心点策略
        custom_strategy = self.config.get('custom_centroid_strategy', {})
        total_blocks = len(self.up_data)

        # 处理负索引（-1表示最后一个区域）
        actual_block_idx = block_idx if block_idx >= 0 else total_blocks + block_idx

        # 调试信息（可选）
        # print(f"{block_name}: 调试信息 - block_idx={block_idx}, actual_block_idx={actual_block_idx}, total_blocks={total_blocks}")
        # print(f"{block_name}: 自定义策略配置: {custom_strategy}")

        # 检查当前区域是否有自定义策略
        strategy_key = None

        # 检查正索引
        if block_idx in custom_strategy:
            strategy_key = block_idx
            # print(f"{block_name}: 找到自定义策略 (使用正索引 {block_idx})")
        # 检查是否是最后一个区域，如果是，检查-1键
        elif block_idx == total_blocks - 1 and -1 in custom_strategy:
            strategy_key = -1
            print(f"{block_name}: 找到自定义策略 (使用负索引 -1，当前是最后一个区域)")
        # 检查其他可能的负索引转换
        elif actual_block_idx in custom_strategy:
            strategy_key = actual_block_idx
            # print(f"{block_name}: 找到自定义策略 (使用转换索引 {actual_block_idx})")
        else:
            pass  # 未找到自定义策略，使用默认配置

        # 应用自定义策略
        if strategy_key is not None:
            strategy = custom_strategy[strategy_key]
            strategy_direction = strategy.get('direction', 'both')

            # 检查是否应用自定义策略
            should_apply_custom = (
                strategy_direction == 'both' or
                strategy_direction == direction
            )

            if should_apply_custom:
                # 使用自定义配置覆盖默认配置
                custom_config = config.copy()
                custom_config.update({
                    'n_clusters': strategy.get('n_clusters', config['n_clusters']),
                    'max_iter': strategy.get('max_iter', config['max_iter']),
                    'n_init': strategy.get('n_init', config['n_init']),
                    'random_state': strategy.get('random_state', config['random_state']),
                    'min_points': strategy.get('min_points', config['min_points'])
                })

                use_real_point = strategy.get('use_real_point', False)

                print(f"{block_name}: 使用自定义策略 - n_clusters={custom_config['n_clusters']}, use_real_point={use_real_point}")

                if use_real_point:
                    return self._compute_real_data_centroid(data_block, block_name)
                else:
                    # 使用自定义配置进行KMeans计算
                    config = custom_config

        if len(data_block) < config['min_points']:
            print(f"{block_name}: 点数不足({len(data_block)} < {config['min_points']})，使用几何中心")
            return self._compute_geometric_centroid(data_block)

        try:
            # 准备KMeans输入数据：只使用经纬度进行聚类
            # 航向由于其周期性，在中心点计算中单独处理
            geo_data = data_block[:, :2]  # 只取经纬度

            # 执行KMeans聚类找到最佳中心点
            kmeans = KMeans(
                n_clusters=config['n_clusters'],
                max_iter=config['max_iter'],
                n_init=config['n_init'],
                random_state=config['random_state']
            )

            kmeans.fit(geo_data)
            geo_centroids = kmeans.cluster_centers_  # 获取所有聚类中心

            # 处理多个中心点的情况
            final_centroids = []

            for i, geo_centroid in enumerate(geo_centroids):
                # 找到距离当前KMeans中心最近的实际数据点
                distances = np.sqrt(np.sum((geo_data - geo_centroid) ** 2, axis=1))
                closest_idx = np.argmin(distances)

                # 计算该区域内航向的圆形均值
                # 定义"该区域"为距离最近点一定范围内的所有点
                distance_threshold = np.percentile(distances, 30)  # 使用30%分位数作为阈值
                nearby_mask = distances <= distance_threshold
                nearby_headings = data_block[nearby_mask, 2]

                # 计算航向的圆形均值
                heading_centroid = self._compute_circular_mean(nearby_headings)

                # 组合最终的中心点：KMeans的经纬度 + 圆形均值航向
                final_centroid = np.array([
                    geo_centroid[0],  # KMeans计算的经度
                    geo_centroid[1],  # KMeans计算的纬度
                    heading_centroid  # 圆形均值航向
                ])

                final_centroids.append(final_centroid)

            print(f"{block_name}: KMeans中心点计算成功，生成了{len(final_centroids)}个中心点")

            # 如果有多个中心点，使用更智能的排序方式确保拟合顺序正确
            if len(final_centroids) > 1:
                final_centroids = self._sort_centroids_by_trajectory_order(
                    final_centroids, data_block, block_name, block_idx
                )
                print(f"{block_name}: 多中心点已按轨迹顺序排序")

            # 如果只有一个中心点，返回单个数组；否则返回列表
            if len(final_centroids) == 1:
                return final_centroids[0]
            else:
                return final_centroids

        except Exception as e:
            print(f"{block_name}: KMeans计算失败 ({e})，使用几何中心")
            return self._compute_geometric_centroid(data_block)

    def _compute_geometric_centroid(self, data_block):
        """计算几何中心点作为备选方案"""
        centroid = np.zeros(3)
        centroid[0] = np.mean(data_block[:, 0])  # 经度均值
        centroid[1] = np.mean(data_block[:, 1])  # 纬度均值
        centroid[2] = self._compute_circular_mean(data_block[:, 2])  # 航向圆形均值
        return centroid

    def _compute_real_data_centroid(self, data_block, block_name):
        """
        选择真实原始数据点作为中心点

        策略：选择距离几何中心最近的真实数据点

        Args:
            data_block: 数据块 (n_points, 3) - [经度, 纬度, 航向]
            block_name: 数据块名称（用于日志）

        Returns:
            真实数据点 [经度, 纬度, 航向]
        """
        try:
            # 计算几何中心作为参考点
            geo_center = np.array([
                np.mean(data_block[:, 0]),  # 经度均值
                np.mean(data_block[:, 1])   # 纬度均值
            ])

            # 计算所有点到几何中心的距离（只考虑经纬度）
            geo_data = data_block[:, :2]
            distances = np.sqrt(np.sum((geo_data - geo_center) ** 2, axis=1))

            # 找到距离几何中心最近的真实数据点
            closest_idx = np.argmin(distances)
            real_centroid = data_block[closest_idx].copy()

            print(f"{block_name}: 选择真实数据点作为中心 - 距离几何中心 {distances[closest_idx]:.6f}")
            return real_centroid

        except Exception as e:
            print(f"{block_name}: 真实数据点选择失败 ({e})，回退到几何中心")
            return self._compute_geometric_centroid(data_block)

    def save_centroids_to_csv(self):
        """
        将上下行中心点保存到CSV文件
        保存路径与图片生成路径相同，和每个block同级
        """
        try:
            # 确保保存目录存在
            save_dir = Path(self.config['save_dir'])
            save_dir.mkdir(parents=True, exist_ok=True)

            # 准备中心点数据
            centroids_data = []

            # 添加上游中心点
            for i, center in enumerate(self.up_centers):
                centroids_data.append({
                    'block_id': i + 1,
                    'direction': 'upstream',
                    'direction_label': 1,
                    'longitude': center[0],
                    'latitude': center[1],
                    'heading': center[2] if len(center) > 2 else 0.0
                })

            # 添加下游中心点
            for i, center in enumerate(self.down_centers):
                centroids_data.append({
                    'block_id': i + 1,
                    'direction': 'downstream',
                    'direction_label': 0,
                    'longitude': center[0],
                    'latitude': center[1],
                    'heading': center[2] if len(center) > 2 else 0.0
                })

            # 转换为DataFrame
            df = pd.DataFrame(centroids_data)

            # 按block_id和direction排序
            df = df.sort_values(['block_id', 'direction_label'], ascending=[True, False])

            # 保存到CSV文件
            csv_path = save_dir / 'centroids_data.csv'
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')

            print(f"✅ 中心点数据已保存至: {csv_path}")
            print(f"   - 上游中心点: {len(self.up_centers)} 个")
            print(f"   - 下游中心点: {len(self.down_centers)} 个")
            print(f"   - 总计: {len(centroids_data)} 个中心点")

            return True

        except Exception as e:
            print(f"❌ 保存中心点数据失败: {e}")
            return False

    def _compute_circular_mean(self, angles_deg):
        """计算角度的圆形均值"""
        angles_rad = np.radians(angles_deg)
        mean_x = np.mean(np.cos(angles_rad))
        mean_y = np.mean(np.sin(angles_rad))
        mean_angle_rad = np.arctan2(mean_y, mean_x)
        return np.degrees(mean_angle_rad) % 360

    def _sort_centroids_by_trajectory_order(self, centroids, data_block, block_name, block_idx):
        """
        根据轨迹顺序对中心点进行排序，确保拟合曲线的连续性

        Args:
            centroids: 中心点列表
            data_block: 原始数据块
            block_name: 数据块名称（用于日志）
            block_idx: 数据块索引

        Returns:
            排序后的中心点列表
        """
        if len(centroids) <= 1:
            return centroids

        try:
            # 获取总的数据块数量
            total_blocks = len(self.up_data)

            # 检查是否是最后一个区域
            is_last_block = (block_idx == total_blocks - 1)

            if is_last_block:
                print(f"{block_name}: 检测到最后一个区域，使用特殊排序策略")

                # 对于最后一个区域，使用更智能的排序策略
                sorted_centroids = self._sort_last_block_centroids(centroids, data_block, block_name)
                return sorted_centroids

            # 对于非最后区域或其他情况，使用原有的经度排序
            sorted_centroids = sorted(centroids, key=lambda x: x[0])  # 按经度排序
            print(f"{block_name}: 使用经度排序")
            return sorted_centroids

        except Exception as e:
            print(f"{block_name}: 排序失败 ({e})，使用原始顺序")
            return centroids

    def _sort_last_block_centroids(self, centroids, data_block, block_name):
        """
        专门处理最后一个区域的中心点排序
        按纬度排序并分配序号：纬度高的序号为0，纬度低的序号为1

        Args:
            centroids: 中心点列表
            data_block: 原始数据块
            block_name: 数据块名称

        Returns:
            排序后的中心点列表（带序号信息）
        """
        if len(centroids) <= 1:
            return centroids

        try:
            # 获取自定义策略配置
            custom_strategy = self.config.get('custom_centroid_strategy', {})
            last_region_config = custom_strategy.get(-1, {})

            # 获取排序配置，默认按纬度排序（纬度高的序号为0）
            sort_by_latitude = last_region_config.get('sort_by_latitude', True)

            if sort_by_latitude:
                # 按纬度排序：纬度高的在前（序号0），纬度低的在后（序号1）
                sorted_centroids = sorted(centroids, key=lambda x: x[1], reverse=True)
                sort_method = "纬度降序"
            else:
                # 按纬度排序：纬度低的在前（序号0），纬度高的在后（序号1）
                sorted_centroids = sorted(centroids, key=lambda x: x[1], reverse=False)
                sort_method = "纬度升序"

            # 输出调试信息，显示中心点序号和位置
            print(f"{block_name}: 按{sort_method}排序，分配序号:")
            for i, centroid in enumerate(sorted_centroids):
                print(f"  序号 {i}: 经度={centroid[0]:.6f}, 纬度={centroid[1]:.6f}, 航向={centroid[2]:.2f}°")

            # 获取连接配置
            connection_config = self.config.get('centroid_connection', {})
            if connection_config.get('debug_connection_info', True):
                connection_index = last_region_config.get('connection_index',
                                 connection_config.get('last_region_connection_index', 0))
                print(f"{block_name}: 上一区域中心点将连接到序号 {connection_index} 的中心点")

            return sorted_centroids

        except Exception as e:
            print(f"{block_name}: 最后区域排序失败 ({e})，回退到纬度排序")
            # 回退策略：简单按纬度降序排序
            return sorted(centroids, key=lambda x: x[1], reverse=True)

    def _apply_centroid_connection_order(self, up_centers, down_centers):
        """
        根据配置调整中心点的连接顺序，确保拟合曲线按正确顺序连接

        Args:
            up_centers: 上游中心点列表
            down_centers: 下游中心点列表

        Returns:
            调整后的 (up_centers, down_centers) 元组
        """
        try:
            # 获取连接配置
            connection_config = self.config.get('centroid_connection', {})
            custom_strategy = self.config.get('custom_centroid_strategy', {})
            last_region_config = custom_strategy.get(-1, {})

            if not connection_config.get('enable_custom_connection', True):
                print("中心点连接控制已禁用，使用默认顺序")
                return up_centers, down_centers

            # 检查是否有最后一个区域的多中心点配置
            if last_region_config.get('n_clusters', 1) <= 1:
                print("最后一个区域只有一个中心点，无需调整连接顺序")
                return up_centers, down_centers

            # 获取连接索引配置
            connection_index = last_region_config.get('connection_index',
                             connection_config.get('last_region_connection_index', 0))

            # 检查是否需要调整最后区域的中心点顺序
            total_blocks = len(self.up_data)
            if total_blocks < 2:
                print("数据块数量不足，无需调整连接顺序")
                return up_centers, down_centers

            # 计算最后一个区域应该有多少个中心点
            expected_last_region_points = last_region_config.get('n_clusters', 1)

            # 检查上游和下游中心点数量
            if len(up_centers) >= expected_last_region_points and len(down_centers) >= expected_last_region_points:
                # 调整最后区域的中心点顺序
                up_centers_adjusted = self._adjust_last_region_order(
                    up_centers, connection_index, expected_last_region_points, "上游"
                )
                down_centers_adjusted = self._adjust_last_region_order(
                    down_centers, connection_index, expected_last_region_points, "下游"
                )

                return up_centers_adjusted, down_centers_adjusted
            else:
                print("中心点数量不符合预期，使用默认顺序")
                return up_centers, down_centers

        except Exception as e:
            print(f"调整中心点连接顺序失败 ({e})，使用默认顺序")
            return up_centers, down_centers

    def _adjust_last_region_order(self, centers, connection_index, expected_points, direction):
        """
        调整最后一个区域的中心点顺序

        Args:
            centers: 中心点列表
            connection_index: 连接索引（0或1）
            expected_points: 最后区域预期的中心点数量
            direction: 方向标识（用于日志）

        Returns:
            调整后的中心点列表
        """
        if len(centers) < expected_points:
            return centers

        # 获取最后区域的中心点
        last_region_points = centers[-expected_points:]
        other_points = centers[:-expected_points]

        # 根据连接索引调整顺序
        if connection_index == 1 and len(last_region_points) >= 2:
            # 如果连接索引为1，交换最后区域的两个中心点顺序
            adjusted_last_points = [last_region_points[1], last_region_points[0]]
            print(f"{direction}: 根据连接索引 {connection_index}，交换最后区域中心点顺序")
        else:
            # 连接索引为0或只有一个点，保持原顺序
            adjusted_last_points = last_region_points
            print(f"{direction}: 根据连接索引 {connection_index}，保持最后区域中心点顺序")

        # 重新组合中心点列表
        adjusted_centers = other_points + adjusted_last_points

        return adjusted_centers

    def _perform_dbscan_clustering(self, input_data):
        """执行DBSCAN聚类（三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        cut = self.config['dbscan']['cut']
        base_eps = self.config['dbscan']['base_eps']
        min_samples = self.config['dbscan']['min_samples']
        max_eps = self.config['dbscan']['max_eps']

        iteration = int(np.ceil(len(input_data) / cut))
        labels_result = np.zeros(len(input_data), dtype=int)

        def cluster_block_with_dbscan(block):
            normalized_block = self._normalize_3d_data(block)
            eps = base_eps
            while eps <= max_eps:
                preds = DBSCAN(eps=eps, min_samples=min_samples).fit_predict(normalized_block)
                valid = [lbl for lbl in set(preds) if lbl != -1]
                if len(valid) >= 2:
                    sizes = {lbl: np.sum(preds == lbl) for lbl in valid}
                    top2 = sorted(sizes, key=lambda x: sizes[x], reverse=True)[:2]
                    block_labels = np.zeros(len(preds), dtype=int)
                    block_labels[preds == top2[0]] = 1
                    block_labels[preds == top2[1]] = 0
                    return block_labels
                eps *= 0.8
            return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            block_labels = cluster_block_with_dbscan(block)
            if block_labels is None:
                block_labels = self._fallback_clustering(block)
            labels_result[start:end] = block_labels

        return labels_result

    def _perform_kmeans_clustering(self, input_data):
        """执行KMeans聚类（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        n_clusters = self.config['kmeans']['n_clusters']
        init = self.config['kmeans']['init']
        max_iter = self.config['kmeans']['max_iter']
        n_init = self.config['kmeans']['n_init']
        random_state = self.config['kmeans']['random_state']
        cut = self.config['kmeans'].get('cut', 100)

        iteration = int(np.ceil(len(input_data) / cut))
        labels_result = np.zeros(len(input_data), dtype=int)

        def cluster_block_with_kmeans(block):
            if len(block) < n_clusters:
                return None
            try:
                normalized_block = self._normalize_3d_data(block)
                kmeans = KMeans(
                    n_clusters=n_clusters,
                    init=init,
                    max_iter=max_iter,
                    n_init=n_init,
                    random_state=random_state
                )
                preds = kmeans.fit_predict(normalized_block)
                unique_labels = np.unique(preds)
                if len(unique_labels) >= 2:
                    sizes = {lbl: np.sum(preds == lbl) for lbl in unique_labels}
                    top2 = sorted(sizes, key=lambda x: sizes[x], reverse=True)[:2]
                    block_labels = np.zeros(len(preds), dtype=int)
                    block_labels[preds == top2[0]] = 1
                    block_labels[preds == top2[1]] = 0
                    return block_labels
                else:
                    return None
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            block_labels = cluster_block_with_kmeans(block)
            if block_labels is None:
                block_labels = self._fallback_clustering(block)
            labels_result[start:end] = block_labels

        return labels_result

    def _perform_spectral_clustering(self, input_data):
        """执行谱聚类（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        min_clusters = self.config['spectral']['min_clusters']
        max_clusters = self.config['spectral']['max_clusters']
        cut = self.config['spectral'].get('cut', 100)

        iteration = int(np.ceil(len(input_data) / cut))
        labels_result = np.zeros(len(input_data), dtype=int)

        def cluster_block_with_spectral(block):
            if len(block) < max(min_clusters, 3):
                return None
            try:
                normalized_block = self._normalize_3d_data(block)
                spectral = SpectralClusterer(
                    min_clusters=min_clusters,
                    max_clusters=max_clusters,
                )
                preds = spectral.predict(normalized_block)
                unique_labels = np.unique(preds)
                if len(unique_labels) >= 2:
                    sizes = {lbl: np.sum(preds == lbl) for lbl in unique_labels}
                    top2 = sorted(sizes, key=lambda x: sizes[x], reverse=True)[:2]
                    block_labels = np.zeros(len(preds), dtype=int)
                    block_labels[preds == top2[0]] = 1
                    block_labels[preds == top2[1]] = 0
                    return block_labels
                else:
                    return None
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            block_labels = cluster_block_with_spectral(block)
            if block_labels is None:
                block_labels = self._fallback_clustering(block)
            labels_result[start:end] = block_labels

        return labels_result

    def _perform_mix2_clustering(self, input_data):
        """执行KMeans和谱聚类的混合方法（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        cut = self.config.get('mix_cut', 100)
        iteration = int(np.ceil(len(input_data) / cut))
        labels_result = np.zeros(len(input_data), dtype=int)

        kmeans_config = self.config['kmeans']
        spectral_config = self.config['spectral']

        def cluster_block_with_mix2(block):
            if len(block) < 3:
                return None
            try:
                normalized_block = self._normalize_3d_data(block)

                # 执行KMeans
                kmeans = KMeans(
                    n_clusters=kmeans_config['n_clusters'],
                    init=kmeans_config['init'],
                    max_iter=kmeans_config['max_iter'],
                    n_init=kmeans_config['n_init'],
                    random_state=kmeans_config['random_state']
                )
                kmeans_labels = kmeans.fit_predict(normalized_block)

                # 执行谱聚类
                spectral = SpectralClusterer(
                    min_clusters=spectral_config['min_clusters'],
                    max_clusters=spectral_config['max_clusters'],
                )
                spectral_labels = spectral.predict(normalized_block)

                # 合并结果
                block_labels = np.zeros_like(kmeans_labels)
                block_labels[(kmeans_labels == 1) & (spectral_labels == 1)] = 1
                return block_labels
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            block_labels = cluster_block_with_mix2(block)
            if block_labels is None:
                block_labels = self._fallback_clustering(block)
            labels_result[start:end] = block_labels

        return labels_result

    def _perform_mix3_clustering(self, input_data):
        """执行KMeans、谱聚类和DBSCAN的混合方法（分块处理，三维数据）"""
        if input_data is None:
            print("数据未读取，无法聚类")
            return False

        cut = self.config.get('mix_cut', 100)
        iteration = int(np.ceil(len(input_data) / cut))
        labels_result = np.zeros(len(input_data), dtype=int)

        kmeans_config = self.config['kmeans']
        spectral_config = self.config['spectral']
        dbscan_config = self.config['dbscan']

        def cluster_block_with_mix3(block):
            if len(block) < 3:
                return None
            try:
                normalized_block = self._normalize_3d_data(block)

                # 执行KMeans
                kmeans = KMeans(
                    n_clusters=kmeans_config['n_clusters'],
                    init=kmeans_config['init'],
                    max_iter=kmeans_config['max_iter'],
                    n_init=kmeans_config['n_init'],
                    random_state=kmeans_config['random_state']
                )
                kmeans_labels = kmeans.fit_predict(normalized_block)

                # 执行谱聚类
                spectral = SpectralClusterer(
                    min_clusters=spectral_config['min_clusters'],
                    max_clusters=spectral_config['max_clusters'],
                )
                spectral_labels = spectral.predict(normalized_block)

                # 执行DBSCAN
                dbscan_labels = DBSCAN(
                    eps=dbscan_config['base_eps'],
                    min_samples=dbscan_config['min_samples']
                ).fit_predict(normalized_block)

                # 合并结果
                valid_dbscan = (dbscan_labels != -1)
                block_labels = np.zeros_like(kmeans_labels)
                block_labels[(kmeans_labels == 1) & (spectral_labels == 1) & (dbscan_labels == 1) & valid_dbscan] = 1
                return block_labels
            except Exception:
                return None

        # 分段聚类
        for i in range(iteration):
            start, end = i * cut, min((i + 1) * cut, len(input_data))
            block = input_data[start:end]
            block_labels = cluster_block_with_mix3(block)
            if block_labels is None:
                block_labels = self._fallback_clustering(block)
            labels_result[start:end] = block_labels

        return labels_result

    def fit_splines(self):
        """拟合样条曲线（只对经纬度进行拟合，航向用于显示）"""
        if self.up_red is None or self.down_red is None:
            print("请先计算中心点")
            return False

        k = self.config['spline_degree']

        self.splines = {}
        for name, pts in [('up', self.up_red), ('down', self.down_red)]:
            if pts.shape[0] > k:
                t = np.arange(len(pts))
                # 只对经纬度进行样条拟合
                self.splines[name] = (
                    splrep(t, pts[:, 0], s=0),  # 经度样条
                    splrep(t, pts[:, 1], s=0),  # 纬度样条
                    t.min(),
                    t.max()
                )
            else:
                print(f"警告: {name} 数据点不足，无法拟合样条曲线")

        return True

    def filter_noise_points(self):
        """基于局部密度分析筛选噪点，使用KD-Tree计算近邻（仅使用经纬度）"""
        from sklearn.neighbors import KDTree

        if not self.orig_input or not self.labels:
            print("数据不完整，无法进行噪点筛选")
            return False

        # 获取配置参数
        n_neighbors = self.config['noise_filter']['n_neighbors']
        min_ratio = self.config['noise_filter']['min_ratio']

        # 用于存储筛选后的数据
        filtered_up_data = []
        filtered_down_data = []

        # 遍历每个数据块
        for block_idx in range(len(self.orig_input)):
            input_data = self.orig_input[block_idx]
            labels = self.labels[block_idx]

            if len(input_data) < n_neighbors:
                print(f"数据块 {block_idx} 的点数少于所需邻居数，跳过噪点筛选")
                filtered_up_data.append(input_data[labels == 1])
                filtered_down_data.append(input_data[labels == 0])
                continue

            # 准备用于KD-Tree的特征数据：只使用经纬度
            features = np.column_stack([
                input_data[:, 0],  # 经度
                input_data[:, 1],  # 纬度
            ])

            # 构建KD-Tree（只基于地理位置）
            tree = KDTree(features)

            # 查找每个点的最近邻
            distances, indices = tree.query(features, k=n_neighbors)

            # 计算每个点邻域内同标签点的比例
            is_noise = np.zeros(len(input_data), dtype=bool)
            for i in range(len(input_data)):
                neighbor_labels = labels[indices[i]]
                same_label_ratio = np.sum(neighbor_labels == labels[i]) / n_neighbors
                is_noise[i] = same_label_ratio < min_ratio

            # 分离非噪点数据
            up_mask = (labels == 1) & ~is_noise
            down_mask = (labels == 0) & ~is_noise

            filtered_up = input_data[up_mask]
            filtered_down = input_data[down_mask]

            # 存储筛选后的数据
            filtered_up_data.append(filtered_up)
            filtered_down_data.append(filtered_down)

            # 打印筛选结果
            removed_up = np.sum(labels == 1) - len(filtered_up)
            removed_down = np.sum(labels == 0) - len(filtered_down)
            print(f"数据块 {block_idx}: 剔除 {removed_up} 个上游噪点, {removed_down} 个下游噪点")

        # 更新数据
        self.up_data = filtered_up_data
        self.down_data = filtered_down_data

        total_up = sum(len(up) for up in filtered_up_data)
        total_down = sum(len(down) for down in filtered_down_data)
        print(f"噪点筛选完成: 保留上游 {total_up} 点, 下游 {total_down} 点")

        return True

    def run_full_pipeline_with_advanced_features(self, advanced_config=None):
        """运行完整处理管道（带高级功能：自定义聚类 + 标签修正）"""
        print("开始运行三维数据处理与聚类分析管道（高级功能版）- KMeans中心点模块化版本...")

        # 应用高级配置
        if advanced_config:
            self._apply_advanced_config(advanced_config)

        if not self.read_and_partition_data():
            print("数据读取失败，终止处理")
            return False

        if not self.perform_clustering():
            print("聚类分析失败，终止处理")
            return False

        # 应用标签修正（如果启用且有配置）
        if self.enable_label_correction and self.label_corrections:
            print("\n=== 标签修正预览 ===")
            self.preview_label_corrections(self.label_corrections)

            # 询问用户是否继续
            user_input = input("\n是否应用标签修正？(y/n): ").strip().lower()
            if user_input in ['y', 'yes', '是']:
                if not self.apply_label_corrections():
                    print("标签修正失败，终止处理")
                    return False
            else:
                print("跳过标签修正")

        # 直接进行噪点筛选，不进行预先的中心点计算
        if not self.filter_noise_points():
            print("噪点筛选失败，终止处理")
            return False

        # 在噪点筛选后进行中心点计算
        if not self.compute_centroids_kmeans():
            print("KMeans中心点计算失败，终止处理")
            return False

        # 设置up_red和down_red为KMeans计算的中心点
        self.up_red = np.array(self.up_centers) if self.up_centers else np.empty((0, 3))
        self.down_red = np.array(self.down_centers) if self.down_centers else np.empty((0, 3))

        if not self.fit_splines():
            print("样条拟合失败，终止处理")
            return False

        # 使用可视化模块生成所有图片
        self.visualizer.visualize_all_results(
            self.orig_input,
            self.labels,
            self.up_red,
            self.down_red,
            self.splines
        )

        # 使用可视化模块保存标签数据
        if not self.visualizer.save_labeled_data(
            self.up_data,
            self.down_data,
            self.config['save_dir']
        ):
            print("标签数据保存失败，终止处理")
            return False

        # 输出处理信息
        if self.custom_clustered_blocks:
            print(f"\n使用自定义聚类的数据块: {sorted(list(self.custom_clustered_blocks))}")
        if self.corrected_blocks:
            print(f"已修正标签的数据块: {sorted(list(self.corrected_blocks))}")

        print(f"高级功能处理完成！所有结果已保存至: {self.config['save_dir']}")
        return True

    def _apply_advanced_config(self, advanced_config):
        """应用高级配置"""
        # 设置功能开关
        switches = advanced_config.get('switches', {})
        if switches:
            self.set_feature_switches(
                enable_label_correction=switches.get('enable_label_correction', True),
                enable_custom_clustering=switches.get('enable_custom_clustering', True)
            )

        # 设置自定义聚类配置
        custom_clustering = advanced_config.get('custom_clustering', {})
        if custom_clustering:
            self.set_custom_clustering(custom_clustering)

        # 设置标签修正配置
        label_corrections = advanced_config.get('label_corrections', {})
        if label_corrections:
            self.set_label_corrections(label_corrections)

    # 保留原有接口以兼容性
    def run_full_pipeline_with_correction(self, label_corrections=None):
        """运行完整处理管道（带标签修正功能）- 兼容性接口"""
        advanced_config = {}
        if label_corrections:
            advanced_config['label_corrections'] = label_corrections
        return self.run_full_pipeline_with_advanced_features(advanced_config) 


# 配置参数集中定义
cluster_method = 'dbscan'  # 可选: 'dbscan', 'kmeans', 'spectral', 'mix2', 'mix3'
save_dir = os.path.join(r'E:\实验代码备份\project 1 trajectory cluster\pictures\3', f'{cluster_method}_advanced')

config = {
    'file_path': r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_经纬度航向.csv",
    'save_dir': save_dir,
    'cluster_method': cluster_method,
    'partition_count': 15,  # 目标切分段落数量（总宽度单位）

    # 数据切分模块配置
    'longitude_col_index': 0,  # 经度列索引
    'latitude_col_index': 1,   # 纬度列索引

    # 自定义段落宽度配置（可选）
    # 格式: {段落索引: 宽度倍数}，支持负数索引
    # 例如: {0: 2, -1: 2} 表示第1段和最后1段为2倍宽度
    'custom_partition_lengths': {
        0: 2,    # 第1段使用2倍宽度，扩大首段覆盖范围
        -1: 1    # 最后1段使用2倍宽度，增强末段鲁棒性
    },

    # KMeans中心点计算参数
    'centroid_kmeans': {
        'n_clusters': 1,        # 每个数据块只需要一个中心点
        'max_iter': 300,        # 最大迭代次数
        'n_init': 10,           # 不同初始化的运行次数
        'random_state': 42,     # 随机种子，确保结果可重现
        'min_points': 5         # 最少需要的点数才进行KMeans，否则使用几何中心
    },

    # 特定区域中心点选取策略配置
    'custom_centroid_strategy': {
        # 针对特定区域使用不同的中心点选取策略
        # 格式: {区域索引: {'direction': 'up'/'down'/'both', 'n_clusters': int, 'use_real_point': True/False, ...}}
        # 区域索引从0开始，-1表示最后一个区域
        -1: {
            'direction': 'both',      # 🎯 针对上下游（红色和蓝色散点）
            'n_clusters': 2,          # 🎯 设置最后一个区域的中心点个数为2
            'max_iter': 300,          # 最大迭代次数
            'n_init': 10,             # 不同初始化的运行次数
            'random_state': 42,       # 随机种子
            'min_points': 5,          # 最少需要的点数
            'use_real_point': False,  # 使用KMeans虚拟中心点
            # 🎯 新增：中心点排序和连接配置
            'sort_by_latitude':  False,     # 按纬度排序（True: 纬度高的序号为0，False: 纬度低的序号为0）
            'connection_index': 0         # 上一区域中心点连接到最后区域的哪个中心点（0或1）
        },
        # 🎯 中心点排序和连接说明：
        # - sort_by_latitude: True表示纬度高的序号为0，False表示纬度低的序号为0
        # - connection_index: 0表示上一区域连接到序号0的中心点，1表示连接到序号1的中心点
        # - 序号分配：按纬度排序后，第一个中心点序号为0，第二个中心点序号为1

        # 可以添加更多区域的自定义策略示例：
        # 0: {                                    # 第一个区域
        #     'direction': 'up',                  # 针对上游（红色散点）
        #     'n_clusters': 3,                    # 生成3个中心点
        #     'use_real_point': True,             # 使用真实原始数据点
        #     'sort_by_latitude': True,           # 按纬度排序
        #     'connection_index': 0               # 连接索引
        # },
        # 5: {                                    # 第6个区域
        #     'direction': 'both',                # 上下游都应用
        #     'n_clusters': 2,                    # 每个方向2个中心点
        #     'use_real_point': False,            # 使用虚拟中心点
        #     'sort_by_latitude': False,          # 纬度低的序号为0
        #     'connection_index': 1               # 连接到序号1的中心点
        # }
    },

    # 🎯 中心点连接控制配置
    'centroid_connection': {
        'last_region_connection_index': 0,  # 上一区域中心点默认连接最后区域编号为0的中心点
        'enable_custom_connection': True,   # 是否启用自定义连接控制
        'debug_connection_info': True       # 是否输出连接调试信息

        # 🎯 使用说明：
        # 1. 最后一个区域生成2个中心点时，会按纬度排序并分配序号0和1
        # 2. 默认纬度高的中心点序号为0，纬度低的中心点序号为1
        # 3. last_region_connection_index控制上一区域连接到最后区域的哪个中心点
        # 4. 可以在custom_centroid_strategy的-1配置中用connection_index覆盖默认值
        # 5. 拟合曲线会按照连接顺序进行，确保轨迹的连续性
    },

    # 噪点筛选参数
    'noise_filter': {
        'n_neighbors': 50,  # 计算局部密度时考虑的邻居数量
        'min_ratio': 0.5,   # 邻域内同标签点的最小比例
    },

    # DBSCAN参数（针对三维数据调整）
    'dbscan': {
        'cut': 5000,
        'base_eps': 0.5,
        'min_samples': 5,
        'max_eps': 1.5
    },

    # KMeans参数
    'kmeans': {
        'n_clusters': 2,
        'init': 'k-means++',
        'max_iter': 500,
        'n_init': 10,
        'random_state': 0,
        'cut': 10000
    },

    # 谱聚类参数
    'spectral': {
        'min_clusters': 2,
        'max_clusters': 2,
        'cut': 10000
    },

    # 混合方法的分块大小
    'mix_cut': 100,

    # 样条拟合参数
    'spline_degree': 3,

    # 新增参数
    'target_sheet': 'Sheet1'  # 假设目标sheet名为'Sheet1'
}

# ===== 高级功能配置 =====
advanced_config = {
    # 功能开关
    'switches': {
        'enable_label_correction': True,   # 启用标签修正功能
        'enable_custom_clustering': False,  # 启用自定义聚类功能
    },

    # 自定义聚类配置
    'custom_clustering': {
        # 第13组数据使用KMeans，参数更严格
        12: {
            'method': 'spectral',
            'params': {
                'min_clusters': 2,
                'max_clusters': 2,
                'cut': 100
            }
        },
    },

    # 标签修正配置
    'label_corrections': {
        # 如果自定义聚类后仍需要标签修正，可以在这里配置
        # 注意：这会在自定义聚类之后执行
        # 18: 'swap',  # 第19组数据标签交换（如果需要）
        # 19: 'swap'   # 第20组数据标签交换（如果需要）
    }
}

# 主程序
if __name__ == "__main__":
    # 创建处理器实例
    processor = GeoDataProcessor(config)

    # 方式一：使用高级功能配置（推荐）
    if processor.run_full_pipeline_with_advanced_features(advanced_config):
        print("高级功能程序成功完成！")
    else:
        print("程序执行过程中出现错误！")
