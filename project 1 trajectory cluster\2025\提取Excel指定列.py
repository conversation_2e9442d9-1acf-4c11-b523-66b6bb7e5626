"""
Excel文件指定列提取工具
读取多个Excel文件的B、C、H列并合并保存为CSV文件
"""

import pandas as pd
import os
from pathlib import Path


def extract_columns_from_excel():
    """从Excel文件中提取指定列并合并"""
    
    # 要读取的Excel文件列表
    excel_files = [
        r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_1.xlsx",
        r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_2.xlsx",
        r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_3.xlsx",
        r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_4.xlsx",
        r"E:\实验代码备份\ais data\excel_output_长江\切1\筛选结果_5.xlsx"
    ]
    
    # 输出CSV文件路径
    output_file = r"E:\实验代码备份\ais data\excel_output_长江\切1\提取的B_C_H列数据.csv"
    
    # 要提取的列（B=1, C=2, H=7，因为pandas使用0-based索引）
    target_columns = [1, 2, 7]  # B、C、H列
    column_names = ['B列', 'C列', 'H列']  # 给列命名
    
    print("=" * 60)
    print("Excel文件指定列提取工具")
    print("=" * 60)
    print(f"目标列: B列、C列、H列")
    print("=" * 60)
    
    # 存储所有提取的数据
    all_dataframes = []
    total_records = 0
    
    # 逐个读取Excel文件
    for i, file_path in enumerate(excel_files, 1):
        print(f"[{i}/{len(excel_files)}] 正在读取: {Path(file_path).name}")
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"  ⚠️  文件不存在，跳过: {Path(file_path).name}")
                continue
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            if df.empty:
                print(f"  ⚠️  文件为空，跳过: {Path(file_path).name}")
                continue
            
            # 检查文件是否有足够的列
            if df.shape[1] < 8:  # 需要至少8列才能有H列
                print(f"  ⚠️  文件列数不足（只有{df.shape[1]}列），跳过: {Path(file_path).name}")
                continue
            
            # 提取指定列
            try:
                extracted_df = df.iloc[:, target_columns].copy()
                extracted_df.columns = column_names

                all_dataframes.append(extracted_df)
                total_records += len(extracted_df)

                print(f"  ✅ 成功提取 {len(extracted_df)} 条记录")

            except IndexError as e:
                print(f"  ❌ 列索引错误: {e}")
                continue
                
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
            continue
    
    # 检查是否有数据
    if not all_dataframes:
        print("\n❌ 没有成功提取任何数据，处理失败")
        return False
    
    print(f"\n📊 总计处理 {len(all_dataframes)} 个文件，{total_records} 条记录")
    
    # 合并所有数据
    print("正在合并数据...")
    try:
        merged_df = pd.concat(all_dataframes, ignore_index=True)
        
        # 确保输出目录存在
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存为CSV文件
        merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 显示结果统计
        print("\n" + "=" * 60)
        print("🎉 提取完成！")
        print("=" * 60)
        print(f"📄 输出文件: {output_path.name}")
        print(f" 合并后记录数: {len(merged_df)}")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ 合并过程中发生错误: {e}")
        return False


def main():
    """主函数"""
    try:
        success = extract_columns_from_excel()
        
        if success:
            print("\n✅ 程序执行成功")
        else:
            print("\n❌ 程序执行失败")
            
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序执行")
    except Exception as e:
        print(f"\n❌ 程序执行过程中发生错误: {e}")
    
    # 等待用户按键退出
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
