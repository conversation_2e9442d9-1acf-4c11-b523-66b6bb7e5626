# AIS数据聚类高级功能使用说明

## 功能概述

`Multi-clustering3_LabelCorrection_Modular.py` 是一个功能完整的AIS数据聚类分析工具，集成了两大高级功能：

1. **🎯 自定义聚类功能**：为特定数据块设置不同的聚类方法和参数
2. **🔄 标签修正功能**：聚类后修正特定数据块的标签
3. **⚙️ 功能开关**：可以独立启用/禁用各项功能

## 核心功能详解

### 🎯 **自定义聚类功能**

#### 使用场景
- 某些数据块的特征不明显，默认聚类效果不佳
- 不同区域需要不同的聚类策略
- 需要针对特定数据块进行精细调优

#### 配置方式
```python
'custom_clustering': {
    # 第18组数据使用KMeans，参数更严格
    17: {
        'method': 'kmeans',
        'params': {
            'n_clusters': 3,      # 尝试3个聚类中心
            'max_iter': 800,      # 增加迭代次数
            'cut': 5000,          # 减小分块大小
            'n_init': 20          # 增加初始化次数
        }
    },
    # 第19组数据使用DBSCAN，参数更敏感
    18: {
        'method': 'dbscan',
        'params': {
            'base_eps': 0.2,      # 更小的邻域半径
            'min_samples': 8,     # 更多的最小样本数
            'max_eps': 0.8,       # 更小的最大半径
            'cut': 3000           # 更小的分块大小
        }
    }
}
```

#### 支持的聚类方法
- **kmeans**: K均值聚类
- **dbscan**: 基于密度的聚类
- **spectral**: 谱聚类
- **mix2**: KMeans + 谱聚类混合
- **mix3**: KMeans + 谱聚类 + DBSCAN混合

### 🔄 **标签修正功能**

#### 使用场景
- 聚类结果大部分正确，但个别数据块标签相反
- 需要手动调整特定区域的上下游划分

#### 配置方式
```python
'label_corrections': {
    18: 'swap',           # 第19组数据标签交换
    19: 'swap',           # 第20组数据标签交换
    20: custom_labels     # 第21组数据使用自定义标签数组
}
```

### ⚙️ **功能开关**

#### 配置方式
```python
'switches': {
    'enable_label_correction': True,   # 启用标签修正功能
    'enable_custom_clustering': True   # 启用自定义聚类功能
}
```

## 完整配置示例

### 📋 **高级配置模板**

```python
advanced_config = {
    # 功能开关
    'switches': {
        'enable_label_correction': True,   # 启用标签修正
        'enable_custom_clustering': True   # 启用自定义聚类
    },
    
    # 自定义聚类配置
    'custom_clustering': {
        # 针对特征不明显的数据块
        17: {  # 第18组数据
            'method': 'kmeans',
            'params': {
                'n_clusters': 3,      # 增加聚类中心数
                'max_iter': 800,      # 增加迭代次数
                'cut': 5000           # 减小分块大小
            }
        },
        18: {  # 第19组数据
            'method': 'dbscan',
            'params': {
                'base_eps': 0.2,      # 更敏感的参数
                'min_samples': 8,
                'cut': 3000
            }
        }
    },
    
    # 标签修正配置（在自定义聚类后执行）
    'label_corrections': {
        19: 'swap'  # 如果第20组仍需要标签交换
    }
}
```

## 使用方法

### 🚀 **基本使用流程**

```python
# 1. 创建处理器实例
processor = GeoDataProcessor(config)

# 2. 运行高级功能管道
if processor.run_full_pipeline_with_advanced_features(advanced_config):
    print("处理成功完成！")
else:
    print("处理失败！")
```

### 🔧 **分步骤使用**

```python
# 1. 创建处理器
processor = GeoDataProcessor(config)

# 2. 设置功能开关
processor.set_feature_switches(
    enable_label_correction=True,
    enable_custom_clustering=True
)

# 3. 设置自定义聚类
processor.set_custom_clustering({
    17: {'method': 'kmeans', 'params': {'n_clusters': 3}},
    18: {'method': 'dbscan', 'params': {'base_eps': 0.2}}
})

# 4. 设置标签修正
processor.set_label_corrections({
    19: 'swap'
})

# 5. 执行处理流程
processor.read_and_partition_data()
processor.perform_clustering()
processor.apply_label_corrections()
processor.compute_centroids_kmeans()
# ... 其他步骤
```

## 参数调优指南

### 🎯 **KMeans参数调优**

```python
'params': {
    'n_clusters': 2-5,        # 聚类中心数，根据数据复杂度调整
    'max_iter': 300-1000,     # 迭代次数，复杂数据需要更多
    'n_init': 10-50,          # 初始化次数，增加稳定性
    'cut': 1000-10000,        # 分块大小，小块更精细
    'random_state': 42        # 固定随机种子，确保可重现
}
```

### 🎯 **DBSCAN参数调优**

```python
'params': {
    'base_eps': 0.1-0.5,      # 邻域半径，小值更敏感
    'min_samples': 3-15,      # 最小样本数，大值更严格
    'max_eps': 0.5-2.0,       # 最大半径，动态调整范围
    'cut': 1000-10000         # 分块大小
}
```

### 🎯 **谱聚类参数调优**

```python
'params': {
    'min_clusters': 2,        # 最小聚类数
    'max_clusters': 2-5,      # 最大聚类数，允许自动选择
    'cut': 1000-5000          # 分块大小，谱聚类计算量大
}
```

## 实际应用案例

### 📊 **案例：20组数据的优化策略**

假设您有20组数据，问题分布如下：
- 第1-16组：聚类效果良好 ✓
- 第17-18组：特征不明显，需要特殊处理 ⚠️
- 第19-20组：聚类后标签相反 ✗

#### 解决方案配置：

```python
advanced_config = {
    'switches': {
        'enable_label_correction': True,
        'enable_custom_clustering': True
    },
    
    # 针对特征不明显的数据块使用更精细的聚类
    'custom_clustering': {
        16: {  # 第17组
            'method': 'kmeans',
            'params': {
                'n_clusters': 3,      # 增加聚类中心
                'max_iter': 800,      # 增加迭代次数
                'cut': 3000           # 减小分块大小
            }
        },
        17: {  # 第18组
            'method': 'mix2',         # 使用混合方法
            'params': {
                'cut': 2000           # 更小的分块
            }
        }
    },
    
    # 修正标签相反的数据块
    'label_corrections': {
        18: 'swap',  # 第19组标签交换
        19: 'swap'   # 第20组标签交换
    }
}
```

#### 预期效果：
```
处理前:
- 第17-18组: 聚类效果差
- 第19-20组: 标签相反

处理后:
- 第17-18组: 使用精细聚类，效果改善
- 第19-20组: 标签修正，结果正确
```

## 输出结果

### 📁 **文件结构**
```
pictures/3/{cluster_method}_advanced/
├── block_1/
├── ...
├── block_17/    # 使用KMeans自定义聚类
├── block_18/    # 使用mix2自定义聚类
├── block_19/    # 标签修正后的结果
├── block_20/    # 标签修正后的结果
└── all_kmeans_labeled_data.csv
```

### 📊 **处理日志示例**
```
功能开关设置:
  - 标签修正功能: 启用
  - 自定义聚类功能: 启用

设置自定义聚类配置: 2 个数据块
  - 数据块 17: 使用 kmeans 方法
    参数: {'n_clusters': 3, 'max_iter': 800, 'cut': 3000}
  - 数据块 18: 使用 mix2 方法
    参数: {'cut': 2000}

数据块 17: 使用自定义聚类方法 kmeans
数据块 18: 使用自定义聚类方法 mix2

=== 标签修正预览 ===
数据块 19 (交换标签):
  修正前: 上游 450 点, 下游 550 点
  修正后: 上游 550 点, 下游 450 点

使用自定义聚类的数据块: [16, 17]
已修正标签的数据块: [18, 19]
```

## 注意事项

### ⚠️ **重要提醒**
1. **执行顺序**：自定义聚类 → 标签修正 → 中心点计算
2. **索引规则**：数据块索引从0开始（第1组=索引0）
3. **参数冲突**：自定义参数会临时覆盖全局配置
4. **性能影响**：精细参数会增加计算时间

### 🔍 **调试建议**
1. **逐步测试**：先测试单个数据块的自定义聚类
2. **参数记录**：记录有效的参数组合
3. **结果对比**：对比默认聚类和自定义聚类的效果
4. **可视化验证**：通过图片验证聚类和修正效果

## 总结

高级功能为AIS数据聚类提供了强大的定制化能力：
- **自定义聚类**：解决特征不明显的数据块
- **标签修正**：快速修正错误的聚类结果
- **功能开关**：灵活控制功能启用状态
- **统一配置**：所有高级设置集中管理

通过合理配置这些功能，可以显著提高聚类分析的准确性和适用性。
