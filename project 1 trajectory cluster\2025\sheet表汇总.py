import pandas as pd
import sys
from pathlib import Path


def merge_excel_sheets(input_file, output_file=None):
    """
    读取Excel文件中所有工作表的数据，合并到第一个工作表的结构中，并输出为CSV文件

    参数:
    input_file (str): 输入Excel文件路径
    output_file (str, optional): 输出CSV文件路径，默认为None(自动生成)
    """
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(input_file)

        # 获取所有表名
        sheet_names = excel_file.sheet_names
        if not sheet_names:
            print("错误: Excel文件中没有工作表!")
            return False

        # 获取第一个工作表作为主表结构
        # 设置header=None避免将数据行作为列名
        df_main = excel_file.parse(sheet_names[0], header=None)

        # 如果主表为空，尝试获取列数
        if df_main.empty:
            print(f"错误: 主表 '{sheet_names[0]}' 为空!")
            return False

        # 生成默认列名
        column_count = len(df_main.columns)
        main_columns = [f"Column{i + 1}" for i in range(column_count)]
        df_main.columns = main_columns

        print(f"发现 {len(sheet_names)} 个工作表，使用 '{sheet_names[0]}' 作为主表结构")
        print(f"主表列名: {', '.join(main_columns)}")

        # 初始化合并后的数据框
        merged_df = df_main.copy()

        # 处理其他工作表
        for sheet_name in sheet_names[1:]:
            # 设置header=None避免将数据行作为列名
            df = excel_file.parse(sheet_name, header=None)

            # 检查工作表是否为空
            if df.empty:
                print(f"警告: 工作表 '{sheet_name}' 为空，跳过")
                continue

            # 设置与主表相同的列名
            df.columns = main_columns

            # 添加到合并后的数据框
            merged_df = pd.concat([merged_df, df], ignore_index=True)
            print(f"已合并工作表 '{sheet_name}' ({len(df)} 行)")

        # 确定输出文件路径
        if output_file is None:
            input_path = Path(input_file)
            output_file = f"{input_path.stem}_合并.csv"

        # 保存为CSV文件
        merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"合并完成! 已保存至 {output_file}")
        print(f"总记录数: {len(merged_df)}")

        return True

    except Exception as e:
        print(f"错误: {str(e)}")
        return False


if __name__ == "__main__":
    # 设置输入和输出路径
    input_file = r"E:\实验代码备份\ais data\excel_output_长江\整合数据.xlsx"  # 修改为实际输入文件路径
    output_file = r"E:\实验代码备份\ais data\excel_output_长江\整合数据_经纬度.csv"  # 修改为实际输出文件路径

    # 执行合并操作
    merge_excel_sheets(input_file, output_file)